# 🚀 Dashboard v6.0 - Filtry Tabeli i Subtelne Kolory

## ✅ Nowe Funkcjonalności

### 1. 🎨 **SUBTELNIEJSZE KOLORY KATEGORII**
**Problem:** "Trochę mniej agresywne kolory na wierszach kategorii"  
**Rozwiązanie:**
```css
PRZED: backgroundColor: '#2c3e50' (ciemnoszary), color: 'white'
PO:    backgroundColor: '#ecf0f1' (jasny szary), color: '#2c3e50', border: '1px solid #bdc3c7'
```

**Rezultat:** Kategorie są teraz bardziej subtelne i czytelne

---

### 2. 🔍 **ZAAWANSOWANE FILTRY TABELI**
**Problem:** "Dodaj filtrowanie tabeli żeby móc wyfiltrować okresy ze spadkami i okresy z zwyżkami"  
**Rozwiązanie:** 3 nowe filtry nad tabelą

---

## 🔧 **Dostępne Filtry**

### 📊 **1. Filtr Typu Okresów**
**Dropdown z opcjami:**
- **📊 Wszystkie okresy** - Pokazuje wszystko (domyślnie)
- **📈 Tylko wzrosty cen** - Tylko okresy z dodatnią zmianą vs bazowy
- **📉 Tylko spadki cen** - Tylko okresy z ujemną zmianą vs bazowy  
- **🔵 Tylko okresy bazowe** - Tylko okresy referencyjne
- **🔴 Tylko piki sezonowe** - Tylko okresy sezonowe (nie bazowe)

### 📏 **2. Minimalna Zmiana Ceny (Slider)**
- **Zakres:** -50% do +50%
- **Krok:** 5%
- **Domyślnie:** -50% (pokazuje wszystko)
- **Funkcja:** Filtruje okresy z zmianą >= wartość

### 📏 **3. Maksymalna Zmiana Ceny (Slider)**
- **Zakres:** -50% do +50%
- **Krok:** 5%
- **Domyślnie:** +50% (pokazuje wszystko)
- **Funkcja:** Filtruje okresy z zmianą <= wartość

---

## 💡 **Przykłady Praktycznego Użycia**

### 🎯 **Scenariusz 1: Znajdź Najlepsze Okazje**
**Ustawienia:**
- Typ: "📈 Tylko wzrosty cen"
- Min zmiana: +15%
- Max zmiana: +50%

**Rezultat:** Pokazuje tylko okresy z wysokimi wzrostami cen (>15%)
```
    ⏰ październik - grudzień    -    Black Friday    1.23    -    [puste]    +17.1%    🔴 Pik
    ⏰ maj                      -    Okazje Wiosenne  1.23    -    [puste]    +17.1%    🔴 Pik
```

### 🎯 **Scenariusz 2: Znajdź Stabilne Kategorie**
**Ustawienia:**
- Typ: "📊 Wszystkie okresy"
- Min zmiana: -5%
- Max zmiana: +5%

**Rezultat:** Pokazuje okresy ze stabilnymi cenami
```
    ⏰ styczeń - kwiecień       -    Okres bazowy     1.05    -    [puste]    0.0% (bazowy)    🔵 BAZOWY
    ⏰ czerwiec - sierpień      -    Wakacyjna        1.08    -    [puste]    +2.9%            🔴 Pik
```

### 🎯 **Scenariusz 3: Analiza Tylko Okresów Bazowych**
**Ustawienia:**
- Typ: "🔵 Tylko okresy bazowe"
- Min/Max: domyślne

**Rezultat:** Pokazuje tylko okresy referencyjne dla porównań
```
    ⏰ styczeń - kwiecień       -    Okres bazowy     1.05    -    [puste]    0.0% (bazowy)    🔵 BAZOWY
    ⏰ styczeń - wrzesień       -    Okres bazowy     0.78    -    [puste]    0.0% (bazowy)    🔵 BAZOWY
```

### 🎯 **Scenariusz 4: Znajdź Spadki Cen**
**Ustawienia:**
- Typ: "📉 Tylko spadki cen"
- Min zmiana: -50%
- Max zmiana: -1%

**Rezultat:** Pokazuje okresy gdzie ceny spadły (rzadkie, ale możliwe)

---

## 🎨 **Nowe Kolorowanie**

### 📂 **Kategorie (Nagłówki)**
```css
Tło: #ecf0f1 (jasny szary)
Tekst: #2c3e50 (ciemnoszary)
Ramka: 1px solid #bdc3c7 (szara)
Font: Pogrubiony
```
**Efekt:** Subtelne, profesjonalne nagłówki

### 🔵 **Okresy Bazowe** (bez zmian)
```css
Tło: #e3f2fd (jasnoniebieski)
Ramka: 2px solid #1976d2 (niebieska, gruba)
Font: Pogrubiony
```

### 🔴 **Piki Sezonowe** (bez zmian)
```css
Tło: #ffebee (jasnoróżowy)
Ramka: 1px solid #d32f2f (czerwona, cienka)
```

---

## 🔄 **Jak Działają Filtry**

### **Logika Filtrowania:**
1. **Najpierw** sprawdzany jest typ okresu (wzrost/spadek/bazowy/pik)
2. **Następnie** sprawdzany jest zakres zmian (min/max)
3. **Tylko okresy** spełniające oba warunki są wyświetlane
4. **Kategorie** (nagłówki) są zawsze wyświetlane

### **Interaktywność:**
- **Zmiany w czasie rzeczywistym** - tabela aktualizuje się natychmiast
- **Kombinowanie filtrów** - wszystkie filtry działają razem
- **Reset** - ustaw "Wszystkie okresy" i suwaki na -50%/+50%

---

## 📊 **Przykład Przed/Po**

### **PRZED (v5.0):**
```
📂 Laptopy                      1.8% - 2.1%    KATEGORIA         [puste]    październik-grudzień    1.23    [puste]         📁 Kategoria
    ⏰ styczeń - kwiecień       -               Okres bazowy      1.05       -                      [puste]    0.0% (bazowy)   🔵 BAZOWY
    ⏰ maj                      -               Okazje Wiosenne   1.23       -                      [puste]    +17.1%          🔴 Pik
    ⏰ październik - grudzień   -               Black Friday      1.23       -                      [puste]    +17.1%          🔴 Pik
```

### **PO (v6.0) - Filtr "Tylko wzrosty >15%":**
```
📂 Laptopy                      1.8% - 2.1%    KATEGORIA         [puste]    październik-grudzień    1.23    [puste]         📁 Kategoria
    ⏰ maj                      -               Okazje Wiosenne   1.23       -                      [puste]    +17.1%          🔴 Pik
    ⏰ październik - grudzień   -               Black Friday      1.23       -                      [puste]    +17.1%          🔴 Pik
```

**Rezultat:** Ukryty okres bazowy (0% zmiana), pokazane tylko wysokie wzrosty

---

## 🚀 **Korzyści Nowych Filtrów**

### ✅ **Analiza Celowana**
- **Szybkie znajdowanie** okresów o określonych charakterystykach
- **Fokus na wzrostach** - najlepsze okresy na inwestycje CPC
- **Analiza stabilności** - okresy z małymi zmianami cen

### ✅ **Lepsze UX**
- **Subtelniejsze kolory** - mniej męczące dla oczu
- **Interaktywne filtry** - natychmiastowe rezultaty
- **Kombinowanie warunków** - precyzyjne wyszukiwanie

### ✅ **Praktyczne Zastosowania**
- **Planowanie budżetu** - focus na wysokie wzrosty
- **Analiza konkurencji** - stabilne vs zmienne kategorie
- **Optymalizacja CPC** - identyfikacja najlepszych okresów

---

## 🚀 **Uruchomienie**
```bash
python dashboard_sezonowosc.py
```
**URL:** http://127.0.0.1:8050/

---

**🎉 Dashboard ma teraz zaawansowane filtry i subtelniejsze kolory - dokładnie jak chciałeś!**
