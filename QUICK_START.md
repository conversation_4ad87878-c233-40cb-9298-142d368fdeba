# 🚀 <PERSON><PERSON><PERSON><PERSON> Start - Dashboard Sezonowości

## ⚡ Uruchomienie w 3 krokach

### 1. Instalac<PERSON> z<PERSON>
```bash
pip install plotly dash numpy pandas
```

### 2. Uruchomienie aplikacji
```bash
python dashboard_sezonowosc.py
```

### 3. Otwórz w przeglądarce
```
http://127.0.0.1:8050/
```

## 📊 Co zobaczysz?

### 🎯 Filtr Kategorii
- Dropdown z listą kategorii głównych
- Automatyczna aktualizacja wszystkich wykresów

### 📅 Wykres Gantta - Oś Czasu
- **Niebieski** = Okresy bazowe (standardowy ruch)
- **Czerwony** = Piki sezonowe (wzmożone zainteresowanie)
- Hover pokazuje szczegóły okresu

### 📋 Tabela Analityczna
- Porównanie cen między okresami
- **Zielone** = wz<PERSON>t ceny vs okres bazowy
- **Czerwone** = spadek ceny vs okres bazowy
- Zakresy prowizji dla każdego okresu

## 🎨 Przykładowe Kategorie

### 💻 Komputery
- Okres bazowy: styczeń-kwiecień
- Pik: maj (Okazje Wiosenne)
- Pik: październik-grudzień (Black Friday)

### 🏠 Sprzęt AGD  
- Okres bazowy: styczeń-maj
- Pik: czerwiec-sierpień (Wakacje)
- Pik: październik-grudzień (Black Friday)

### 🚗 Motoryzacja
- Okres bazowy: luty-czerwiec
- Spadek: lipiec-sierpień (Wakacje)
- Pik: październik-grudzień (Black Friday)

## 🔧 Rozwiązywanie problemów

### Port zajęty?
Zmień port w pliku `dashboard_sezonowosc.py`:
```python
app.run(debug=True, host='127.0.0.1', port=8051)
```

### Brak danych?
Aplikacja automatycznie wygeneruje przykładowe dane testowe.

### Błąd importu?
```bash
pip install --upgrade plotly dash pandas numpy
```

## 📈 Interpretacja Wyników

### Okresy Bazowe (Niebieski)
- Standardowy poziom ruchu
- Punkt odniesienia dla porównań
- Stabilne ceny

### Piki Sezonowe (Czerwony)
- Wzmożone zainteresowanie
- Potencjał na wyższe stawki CPC
- Często wyższe ceny

### Analiza Cen
- **+X%** = Cena wyższa niż w okresie bazowym
- **-X%** = Cena niższa niż w okresie bazowym
- **Okres bazowy** = Punkt odniesienia (0%)

## 🎯 Zastosowanie Biznesowe

### Model CPC
- Identyfikacja okresów wysokiego ruchu
- Optymalizacja budżetu marketingowego
- Planowanie kampanii sezonowych

### Model "Kup Teraz"
- Analiza zachowań cenowych
- Ocena rentowności okresów
- Strategia cenowa konkurencyjna

---
**Gotowe do analizy! 🎉**
