# Dashboard Analizy Sezonowości Kategorii

## Opis
Interaktywny dashboard analityczny do analizy sezonowości kategorii produktów z wykorzystaniem danych CPC i prowizji "Kup Teraz". Dashboard pomaga menedżerom kategorii w podejmowaniu decyzji dotyczących optymalizacji stawek CPC i strategii cenowych.

## 🚀 Nowe Funkcjonalności v2.0

### 1. 🎯 Inteligentny Filtr Kategorii
- **Dropdown z opisem** - jasne objaśnienie funkcji
- **Legenda na żywo** - wyjaśnienie kolorów i znaczeń
- Automatyczna aktualizacja wszystkich komponentów

### 2. 📅 Zaawansowany Kalendarz Sezonowości (Wykres Gantta)
- **Profesjonalne kolorowanie**:
  - 🔵 **Okres Bazowy** - standardowy poziom ruchu i cen
  - 🔴 **Pik <PERSON>y** - wz<PERSON>żone zainteresowanie, potencjał na wyższe stawki
- **Bogate tooltips** z informacjami o cenach i typach sezonowości
- **Czytelne etykiety** osi i legendy

### 3. 🥧 Wykres Kołowy - Rozkład Okresów
- **Wizualizacja proporcji** różnych typów sezonowości
- **Kolorowe mapowanie** typów okresów
- **Interaktywne tooltips** z procentowym udziałem

### 4. 📋 Hierarchiczna Tabela Analityczna
- **Struktura drzewiasta**: Kategorie → Okresy
- **Wizualne wcięcia** dla lepszej czytelności
- **Kolorowe statusy**:
  - 🔵 **Okresy bazowe** - niebieskie tło
  - 🔴 **Piki sezonowe** - czerwone tło
  - 📁 **Kategorie** - ciemne nagłówki
- **Szczegółowe kolumny**:
  - 📂 Kategoria/Okres (z ikonami)
  - 🏷️ Typ Sezonowości
  - 💰 Prowizja (%)
  - 💵 Cena (PLN)
  - 📈 Zmiana vs Bazowy (kolorowane)
  - 🎯 Status (z ikonami)

### 5. 💡 Kompleksowe Objaśnienia
- **Legenda na dashboardzie** - wyjaśnienie kolorów
- **Opisy sekcji** - co pokazuje każdy wykres
- **Objaśnienia kolumn** - szczegółowe wyjaśnienie każdej kolumny tabeli

## Wymagania Systemowe

### Python 3.7+
```bash
pip install pandas plotly dash numpy
```

### Struktura Plików
```
projekt/
├── dashboard_sezonowosc.py     # Główny plik aplikacji
├── README_Dashboard.md         # Ten plik
└── report/
    └── CENEO_CPC_COMMISION_MODEL.csv  # Plik z danymi
```

## Uruchomienie

### 1. Instalacja zależności
```bash
pip install pandas plotly dash numpy
```

### 2. Uruchomienie aplikacji
```bash
python dashboard_sezonowosc.py
```

### 3. Dostęp do dashboardu
Otwórz przeglądarkę i przejdź do:
```
http://127.0.0.1:8050/
```

## Struktura Danych Wejściowych

Dashboard oczekuje pliku CSV o następującej strukturze:
- `leaf_category` - kategoria liść
- `kategoria_glowna` - kategoria główna
- `pelna_sciezka` - pełna ścieżka kategorii
- `min_commision_perc` - minimalna prowizja (%)
- `max_commision_perc` - maksymalna prowizja (%)
- `cena_bazowa_kategorii_pln` - cena bazowa kategorii
- `analiza_cen_w_okresach` - JSON z analizą okresów sezonowych

### Format JSON dla analizy okresów:
```json
{
  "analiza_cen_w_okresach": [
    {
      "okres": "październik - grudzień",
      "typ_sezonowosci": "Black Friday",
      "cena_w_okresie": "1.49",
      "roznica_bezwzgledna_pln": "0.21",
      "roznica_procentowa": "0.164"
    }
  ]
}
```

## Logika Biznesowa

### Model CPC
- **Okres bazowy** - standardowy okres o przeciętnym ruchu
- **Piki sezonowe** - okresy wzmożonego zainteresowania
- Analiza pozwala na efektywne zarządzanie budżetem marketingowym

### Model "Kup Teraz"
- Porównanie cen między okresami
- Ocena rentowności w różnych okresach sezonowych
- Analiza zachowań cenowych sprzedawców

## Przykładowe Kategorie i Okresy

### Komputery
- **Okres bazowy**: styczeń - kwiecień
- **Okazje Wiosenne**: maj
- **Wakacyjna**: czerwiec - wrzesień
- **Black Friday**: październik - grudzień

### Sprzęt AGD
- **Okres bazowy**: styczeń - maj
- **Wakacyjna**: czerwiec - sierpień
- **Jesienna**: wrzesień
- **Black Friday**: październik - grudzień

### Motoryzacja
- **Wyprzedaże Noworoczne**: styczeń
- **Okres bazowy**: luty - czerwiec
- **Wakacyjna**: lipiec - sierpień
- **Jesienna**: wrzesień
- **Black Friday**: październik - grudzień

## Rozwiązywanie Problemów

### Brak pliku danych
Jeśli plik `report/CENEO_CPC_COMMISION_MODEL.csv` nie istnieje, aplikacja automatycznie wygeneruje przykładowe dane testowe.

### Błędy parsowania JSON
Aplikacja pomija wiersze z nieprawidłowym formatem JSON i kontynuuje przetwarzanie pozostałych danych.

### Port zajęty
Jeśli port 8050 jest zajęty, zmień port w linii:
```python
app.run_server(debug=True, host='127.0.0.1', port=8051)
```

## Autor
Dashboard stworzony zgodnie z wymaganiami analizy sezonowości kategorii produktów dla optymalizacji stawek CPC i strategii cenowych.
