#!/usr/bin/env python3
"""
Scraper Ceneo Commission Only - pobiera kategorie z modelu prowizyjnego "Commission Only".
"""

import logging
import csv
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CeneoCommissionOnlyScraper:
    def __init__(self):
        self.driver = None
        self.csv_file = 'ceneo_commission_only_data.csv'
        self.processed_nodes = set()
        
    def setup_driver(self):
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Przeglądarka uruchomiona")

    def navigate_to_page(self):
        self.driver.get("https://shops.ceneo.pl/Pricelist/Commission")
        logger.info("Nawigacja do strony Commission Only zakończona")
        
        try:
            WebDriverWait(self.driver, 30).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
            )
            logger.info("Splash screen zniknął")
        except:
            logger.info("Splash screen nie został wykryty")
        
        time.sleep(5)
        logger.info("Strona Commission Only załadowana")

    def get_category_name(self, node):
        try:
            text = node.text.strip()
            if not text:
                return None
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            for line in lines:
                if line not in ['chevron_right', 'expand_more'] and '%' not in line and 'zł' not in line:
                    return line
            
            return None
        except Exception as e:
            logger.warning(f"Błąd pobierania nazwy kategorii: {e}")
            return None

    def get_commission_info(self, node):
        """Wyciąga informacje o prowizjach Commission Only z węzła."""
        try:
            text = node.text.strip()
            if not text:
                return []
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            commission_data = []
            
            for line in lines:
                if line in ['chevron_right', 'expand_more']:
                    continue
                
                # Szukaj linii z prowizjami procentowymi (zawierają % ale nie :)
                if '%' in line and ':' not in line and 'zł' not in line:
                    commission_line = line.strip()
                    
                    if ' - ' in commission_line:
                        # Zakres prowizji: min% - max%
                        parts = commission_line.split(' - ')
                        if len(parts) == 2:
                            min_commission = parts[0].strip().replace('%', '')
                            max_commission = parts[1].strip().replace('%', '')
                            commission_data.append({
                                'period': 'Commission Only',
                                'min_commission': min_commission,
                                'max_commission': max_commission
                            })
                    else:
                        # Pojedyncza prowizja
                        single_commission = commission_line.replace('%', '').strip()
                        if single_commission:
                            commission_data.append({
                                'period': 'Commission Only',
                                'min_commission': single_commission,
                                'max_commission': single_commission
                            })
            
            return commission_data
        except Exception as e:
            logger.warning(f"Błąd parsowania prowizji Commission Only: {e}")
            return []

    def has_expand_button(self, node):
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return True
            return False
        except:
            return False

    def get_expand_button(self, node):
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return icon
            return None
        except:
            return None

    def build_category_path_safe(self, current_node, level, all_nodes):
        """Bezpieczna wersja budowania ścieżki kategorii."""
        try:
            current_name = self.get_category_name(current_node)
            if not current_name:
                return "Nieznana kategoria"
            
            if level == 1:
                return current_name
            
            try:
                current_index = all_nodes.index(current_node)
            except ValueError:
                return current_name
            
            # Znajdź rodzica - najbliższy węzeł z niższym poziomem
            for i in range(current_index - 1, -1, -1):
                try:
                    node = all_nodes[i]
                    node_level_str = node.get_attribute('aria-level')
                    if not node_level_str:
                        continue
                        
                    node_level = int(node_level_str)
                    if node_level == level - 1:
                        parent_name = self.get_category_name(node)
                        if parent_name:
                            parent_path = self.build_category_path_safe(node, node_level, all_nodes)
                            return f"{parent_path} > {current_name}"
                        break
                except Exception as e:
                    logger.warning(f"Błąd przy budowaniu ścieżki dla węzła {i}: {e}")
                    continue
            
            return current_name
            
        except Exception as e:
            logger.warning(f"Błąd budowania ścieżki: {e}")
            return f"Błąd ścieżki - {self.get_category_name(current_node) or 'Nieznana'}"

    def get_node_signature(self, node, all_nodes):
        """Tworzy unikalny podpis węzła."""
        try:
            position = all_nodes.index(node)
            level = node.get_attribute('aria-level') or '0'
            name = self.get_category_name(node) or 'unknown'
            return f"{level}_{position}_{name[:20]}"
        except:
            return f"unknown_{id(node)}"

    def save_category_record(self, writer, node, all_nodes, record_count):
        """Zapisuje rekord kategorii Commission Only do CSV."""
        try:
            level_str = node.get_attribute('aria-level')
            if not level_str:
                return record_count
                
            level = int(level_str)
            category_name = self.get_category_name(node)
            
            if not category_name:
                return record_count
            
            # Sprawdź czy węzeł już był przetwarzany
            node_signature = self.get_node_signature(node, all_nodes)
            if node_signature in self.processed_nodes:
                return record_count
            
            self.processed_nodes.add(node_signature)
            
            full_path = self.build_category_path_safe(node, level, all_nodes)
            main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
            parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
            final_category_name = category_name
            
            has_children = self.has_expand_button(node)
            children_count = 0
            
            commission_data = self.get_commission_info(node)
            
            if commission_data:
                for commission_info in commission_data:
                    record = {
                        'pelna_sciezka_kategorii': full_path,
                        'kategoria_glowna': main_category,
                        'parent_kategorii': parent_category,
                        'nazwa_kategorii_ostatecznej': final_category_name,
                        'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                        'liczba_podkategorii': children_count,
                        'model_prowizyjny': commission_info['period'],
                        'prowizja_minimalna_procent': commission_info['min_commission'],
                        'prowizja_maksymalna_procent': commission_info['max_commission']
                    }
                    writer.writerow(record)
                    record_count += 1
            else:
                record = {
                    'pelna_sciezka_kategorii': full_path,
                    'kategoria_glowna': main_category,
                    'parent_kategorii': parent_category,
                    'nazwa_kategorii_ostatecznej': final_category_name,
                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                    'liczba_podkategorii': children_count,
                    'model_prowizyjny': 'Brak danych Commission Only',
                    'prowizja_minimalna_procent': 'N/A',
                    'prowizja_maksymalna_procent': 'N/A'
                }
                writer.writerow(record)
                record_count += 1
            
            return record_count
            
        except Exception as e:
            logger.warning(f"Błąd zapisywania rekordu Commission Only: {e}")
            return record_count

    def run_commission_scraping(self):
        """Uruchamia scraping wszystkich kategorii Commission Only."""
        logger.info("Rozpoczęcie scrapingu Commission Only...")
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
            fieldnames = [
                'pelna_sciezka_kategorii', 'kategoria_glowna', 'parent_kategorii',
                'nazwa_kategorii_ostatecznej', 'czy_kategoria_ostateczna', 'liczba_podkategorii', 
                'model_prowizyjny', 'prowizja_minimalna_procent', 'prowizja_maksymalna_procent'
            ]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            
            record_count = 0
            iteration = 0
            max_iterations = 10  # Zabezpieczenie przed nieskończoną pętlą
            
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"=== ITERACJA {iteration} ===")
                
                try:
                    # Znajdź wszystkie węzły
                    all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                    logger.info(f"Znaleziono {len(all_nodes)} węzłów")
                    
                    if not all_nodes:
                        logger.warning("Brak węzłów - przerywanie")
                        break
                    
                    # Zapisz WSZYSTKIE obecne węzły
                    nodes_processed = 0
                    for node in all_nodes:
                        try:
                            old_count = record_count
                            record_count = self.save_category_record(writer, node, all_nodes, record_count)
                            if record_count > old_count:
                                nodes_processed += 1
                            
                            if record_count % 100 == 0 and record_count > 0:
                                logger.info(f"Zapisano {record_count} rekordów...")
                                file.flush()
                        except Exception as e:
                            logger.warning(f"Błąd przetwarzania węzła: {e}")
                            continue
                    
                    logger.info(f"Przetworzono {nodes_processed} nowych węzłów")
                    
                    # Znajdź węzły do rozwinięcia
                    nodes_to_expand = []
                    for node in all_nodes:
                        try:
                            if self.has_expand_button(node):
                                nodes_to_expand.append(node)
                        except:
                            continue
                    
                    logger.info(f"Węzłów do rozwinięcia: {len(nodes_to_expand)}")
                    
                    if not nodes_to_expand:
                        logger.info("Brak węzłów do rozwinięcia - koniec")
                        break
                    
                    # Rozwiń węzły (maksymalnie 50 na raz żeby nie przeciążyć)
                    expanded_count = 0
                    max_expand = min(50, len(nodes_to_expand))
                    
                    for i, node in enumerate(nodes_to_expand[:max_expand]):
                        try:
                            expand_button = self.get_expand_button(node)
                            if expand_button:
                                self.driver.execute_script("arguments[0].click();", expand_button)
                                expanded_count += 1
                                time.sleep(0.5)
                                
                                if expanded_count % 10 == 0:
                                    logger.info(f"Rozwinięto {expanded_count}/{max_expand} węzłów...")
                                    time.sleep(2)
                                    
                        except Exception as e:
                            logger.warning(f"Błąd rozwijania węzła {i}: {e}")
                            continue
                    
                    logger.info(f"Rozwinięto {expanded_count} węzłów")
                    time.sleep(5)  # Dłuższa pauza na załadowanie
                    
                except Exception as e:
                    logger.error(f"Błąd w iteracji {iteration}: {e}")
                    time.sleep(10)
                    continue
            
            logger.info(f"Zapisano łącznie {record_count} rekordów do {self.csv_file}")

    def cleanup(self):
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka zamknięta")

    def run(self):
        try:
            logger.info("=== ROZPOCZĘCIE SCRAPINGU COMMISSION ONLY ===")
            
            self.setup_driver()
            self.navigate_to_page()
            self.run_commission_scraping()
            
            logger.info("=== SCRAPING COMMISSION ONLY ZAKOŃCZONY POMYŚLNIE ===")
            
        except Exception as e:
            logger.error(f"Błąd podczas scrapingu Commission Only: {e}")
        finally:
            self.cleanup()

def main():
    scraper = CeneoCommissionOnlyScraper()
    scraper.run()

if __name__ == "__main__":
    main()
