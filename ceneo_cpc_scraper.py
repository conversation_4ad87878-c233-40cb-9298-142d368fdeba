#!/usr/bin/env python3
"""
Skrypt do scrapingu danych z cennika CPC na stronie Ceneo.
Autor: AI Assistant
Data: 2025-09-23
"""

import time
import re
import csv
import logging
from typing import List, Dict, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import subprocess
import os
from bs4 import BeautifulSoup
import pandas as pd

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ceneo_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CeneoCPCScraper:
    """Klasa do scrapingu danych CPC z Ceneo."""
    
    def __init__(self, headless: bool = False):
        """
        Inicjalizacja scrapera.
        
        Args:
            headless: Czy uruchomić przeglądarkę w trybie headless
        """
        self.url = "https://shops.ceneo.pl/Pricelist/Cpc"
        self.driver = None
        self.headless = headless
        self.data = []
        
    def setup_driver(self) -> None:
        """Konfiguracja i uruchomienie przeglądarki."""
        try:
            # Próba użycia Chrome z systemowym ChromeDriver
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

            # Próba znalezienia ChromeDriver w systemie
            chromedriver_paths = [
                "/usr/local/bin/chromedriver",
                "/opt/homebrew/bin/chromedriver",
                "/usr/bin/chromedriver"
            ]

            chromedriver_path = None
            for path in chromedriver_paths:
                if os.path.exists(path):
                    chromedriver_path = path
                    break

            if chromedriver_path:
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info(f"Przeglądarka Chrome uruchomiona z {chromedriver_path}")
            else:
                # Fallback do Safari jeśli Chrome nie działa
                logger.warning("ChromeDriver nie znaleziony, próba użycia Safari...")
                self.driver = webdriver.Safari()
                logger.info("Przeglądarka Safari została uruchomiona")

            self.driver.implicitly_wait(10)

        except WebDriverException as e:
            logger.error(f"Błąd WebDriver: {e}")
            # Próba użycia Safari jako fallback
            try:
                logger.info("Próba użycia Safari jako alternatywy...")
                self.driver = webdriver.Safari()
                self.driver.implicitly_wait(10)
                logger.info("Safari uruchomione pomyślnie jako alternatywa")
            except Exception as safari_error:
                logger.error(f"Błąd Safari: {safari_error}")
                logger.error("Aby użyć Safari, włącz 'Allow remote automation' w Developer > Safari Settings")
                raise
        except Exception as e:
            logger.error(f"Błąd podczas uruchamiania przeglądarki: {e}")
            raise
    
    def navigate_to_page(self) -> None:
        """Nawigacja do strony Ceneo CPC."""
        try:
            logger.info(f"Nawigacja do strony: {self.url}")
            self.driver.get(self.url)
            
            # Oczekiwanie na załadowanie strony
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Dodatkowe oczekiwanie na załadowanie dynamicznej zawartości
            time.sleep(5)
            logger.info("Strona została załadowana pomyślnie")
            
        except TimeoutException:
            logger.error("Timeout podczas ładowania strony")
            raise
        except Exception as e:
            logger.error(f"Błąd podczas nawigacji do strony: {e}")
            raise
    
    def expand_all_categories(self) -> None:
        """Rozwijanie wszystkich gałęzi drzewa kategorii."""
        try:
            logger.info("Rozpoczęcie rozwijania wszystkich kategorii...")

            # Oczekiwanie na zniknięcie splash screen
            try:
                WebDriverWait(self.driver, 30).until_not(
                    EC.presence_of_element_located((By.ID, "fuse-splash-screen"))
                )
                logger.info("Splash screen zniknął")
            except TimeoutException:
                logger.warning("Splash screen nie zniknął w oczekiwanym czasie")

            # Dodatkowe oczekiwanie na załadowanie drzewa
            time.sleep(10)

            # Sprawdzenie czy drzewo się załadowało
            try:
                tree_element = self.driver.find_element(By.CSS_SELECTOR, "mat-tree")
                logger.info("Drzewo kategorii zostało załadowane")
            except NoSuchElementException:
                logger.error("Nie znaleziono drzewa kategorii")
                raise

            # Rozwijanie wszystkich poziomów kategorii
            expanded_total = 0
            max_iterations = 20  # Zwiększona liczba iteracji dla głębokiej hierarchii

            for iteration in range(max_iterations):
                logger.info(f"--- ITERACJA {iteration + 1} ---")

                # Policz węzły przed iteracją
                nodes_before = len(self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node"))
                logger.info(f"Węzły przed iteracją: {nodes_before}")

                # Znajdź wszystkie przyciski z chevron_right (nierozwinięte)
                chevron_buttons = []
                all_buttons = self.driver.find_elements(By.CSS_SELECTOR, "button[mattreenodetoggle]")

                for btn in all_buttons:
                    try:
                        if btn.is_displayed() and btn.is_enabled() and btn.text.strip() == "chevron_right":
                            btn_id = btn.get_attribute("data-cy")
                            chevron_buttons.append((btn, btn_id))
                    except Exception as e:
                        continue

                logger.info(f"Znaleziono {len(chevron_buttons)} przycisków z chevron_right")

                if not chevron_buttons:
                    logger.info("Brak przycisków z chevron_right - wszystko rozwinięte!")
                    break

                # Kliknij wszystkie przyciski z chevron_right
                buttons_clicked = 0
                for btn, btn_id in chevron_buttons:
                    try:
                        if btn.is_displayed() and btn.is_enabled():
                            logger.debug(f"Klikanie przycisku: {btn_id}")
                            self.driver.execute_script("arguments[0].click();", btn)
                            buttons_clicked += 1
                            expanded_total += 1
                            time.sleep(0.3)  # Krótka pauza
                    except Exception as e:
                        logger.debug(f"Błąd kliknięcia {btn_id}: {e}")
                        continue

                logger.info(f"Kliknięto {buttons_clicked} przycisków")

                if buttons_clicked == 0:
                    logger.info("Nie udało się kliknąć żadnego przycisku")
                    break

                # Pauza na załadowanie nowych elementów
                time.sleep(3)

                # Policz węzły po rozwijaniu
                nodes_after = len(self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node"))
                logger.info(f"Węzły po iteracji: {nodes_after}")
                logger.info(f"Przyrost węzłów: {nodes_after - nodes_before}")

                # Sprawdź poziomy hierarchii
                levels = {}
                all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                for node in all_nodes:
                    try:
                        level = int(node.get_attribute("aria-level"))
                        levels[level] = levels.get(level, 0) + 1
                    except:
                        continue

                logger.info(f"Aktualne poziomy: {dict(sorted(levels.items()))}")

            logger.info(f"Rozwijanie zakończone. Łącznie rozwinięto: {expanded_total} kategorii")

            # Finalne podsumowanie
            final_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            logger.info(f"Finalna liczba węzłów: {len(final_nodes)}")

            # Analiza finalnych poziomów
            final_levels = {}
            for node in final_nodes:
                try:
                    level = int(node.get_attribute("aria-level"))
                    final_levels[level] = final_levels.get(level, 0) + 1
                except:
                    continue

            max_level = max(final_levels.keys()) if final_levels else 0
            logger.info(f"Maksymalny poziom zagnieżdżenia: {max_level}")

            for level in sorted(final_levels.keys()):
                logger.info(f"Poziom {level}: {final_levels[level]} kategorii")

            # Dodatkowe oczekiwanie na załadowanie rozwinięte zawartości
            time.sleep(5)

        except Exception as e:
            logger.error(f"Błąd podczas rozwijania kategorii: {e}")
            raise
    
    def parse_html_content(self) -> None:
        """Parsowanie zawartości HTML za pomocą BeautifulSoup."""
        try:
            logger.info("Rozpoczęcie parsowania zawartości HTML...")
            
            # Pobranie HTML po rozwinięciu wszystkich kategorii
            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'lxml')
            
            # Zapisanie HTML do pliku dla debugowania
            with open('ceneo_page_content.html', 'w', encoding='utf-8') as f:
                f.write(soup.prettify())
            
            # Szukanie struktury drzewa kategorii
            self._extract_category_data(soup)
            
        except Exception as e:
            logger.error(f"Błąd podczas parsowania HTML: {e}")
            raise
    
    def _extract_category_data(self, soup: BeautifulSoup) -> None:
        """
        Wyodrębnienie danych o kategoriach z HTML.

        Args:
            soup: Obiekt BeautifulSoup z zawartością strony
        """
        logger.info("Rozpoczęcie wyodrębniania danych o kategoriach...")

        # Użyj Selenium do bezpośredniego wyodrębnienia danych
        # ponieważ BeautifulSoup może nie zawierać dynamicznej zawartości
        self._extract_data_with_selenium()

    def _extract_data_with_selenium(self) -> None:
        """Wyodrębnienie danych bezpośrednio z Selenium i zapis do CSV."""
        try:
            # Znajdź wszystkie węzły drzewa
            tree_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            logger.info(f"Znaleziono {len(tree_nodes)} węzłów drzewa")

            # Otwórz plik CSV do zapisu
            csv_filename = 'ceneo_cpc_pricelist.csv'
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'pelna_sciezka_kategorii',
                    'kategoria_glowna',
                    'parent_kategorii',
                    'czy_kategoria_ostateczna',
                    'liczba_podkategorii',
                    'okres_czasowy',
                    'cena_minimalna_pln',
                    'cena_maksymalna_pln'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                # Przetwarzaj węzły w grupach według poziomów
                nodes_by_level = {}
                for node in tree_nodes:
                    try:
                        level = int(node.get_attribute("aria-level"))
                        if level not in nodes_by_level:
                            nodes_by_level[level] = []
                        nodes_by_level[level].append(node)
                    except:
                        continue

                logger.info(f"Węzły pogrupowane według poziomów: {dict((k, len(v)) for k, v in nodes_by_level.items())}")

                # Mapa do śledzenia ścieżek kategorii
                category_paths = {}  # node_id -> full_path
                processed_count = 0

                # Przetwarzaj poziom po poziomie
                for level in sorted(nodes_by_level.keys()):
                    logger.info(f"Przetwarzanie poziomu {level} ({len(nodes_by_level[level])} węzłów)")

                    for node in nodes_by_level[level]:
                        try:
                            # Wyodrębnij dane z węzła
                            node_data = self._parse_tree_node_simple(node, level, category_paths)
                            if node_data:
                                for record in node_data:
                                    # Zapisz do CSV na bieżąco
                                    writer.writerow(record)
                                    processed_count += 1

                                    if processed_count % 100 == 0:
                                        logger.info(f"Przetworzono {processed_count} rekordów...")
                                        csvfile.flush()  # Wymuś zapis do pliku

                        except Exception as e:
                            logger.debug(f"Błąd podczas parsowania węzła: {e}")
                            continue

                logger.info(f"Zapisano {processed_count} rekordów do {csv_filename}")
                logger.info(f"Scraping zakończony pomyślnie!")

        except Exception as e:
            logger.error(f"Błąd podczas wyodrębniania danych: {e}")
            raise

    def _build_hierarchy_map(self, tree_nodes) -> Dict[str, Dict]:
        """
        Buduje mapę hierarchii kategorii.

        Args:
            tree_nodes: Lista węzłów drzewa

        Returns:
            Słownik z mapą hierarchii
        """
        hierarchy = {}

        for node in tree_nodes:
            try:
                node_id = node.get_attribute("data-cy")
                aria_level = node.get_attribute("aria-level")

                if not node_id or not aria_level:
                    continue

                level = int(aria_level)

                # Wyodrębnij nazwę kategorii
                category_name = self._extract_category_name_from_node(node)
                if not category_name:
                    continue

                hierarchy[node_id] = {
                    'name': category_name,
                    'level': level,
                    'parent_id': None,
                    'parent_name': '',
                    'full_path': category_name,  # Będzie zaktualizowana później dla poziomów > 1
                    'main_category': category_name if level == 1 else '',
                    'has_children': self._node_has_children(node)
                }

            except Exception as e:
                logger.debug(f"Błąd podczas budowania hierarchii dla węzła: {e}")
                continue

        # Ustaw relacje parent-child i pełne ścieżki
        self._set_parent_relationships(hierarchy, tree_nodes)

        return hierarchy

    def _extract_category_name_from_node(self, node) -> Optional[str]:
        """
        Wyodrębnia nazwę kategorii z węzła.

        Args:
            node: Element Selenium reprezentujący węzeł

        Returns:
            Nazwa kategorii lub None
        """
        try:
            node_text = node.text.strip()
            if not node_text:
                return None

            # Podziel tekst na linie
            lines = [line.strip() for line in node_text.split('\n') if line.strip()]

            # Znajdź nazwę kategorii (pierwsza linia po ikonach)
            for i, line in enumerate(lines):
                # Pomiń ikony Material Design
                if line in ["chevron_right", "expand_more"]:
                    if i + 1 < len(lines):
                        return lines[i + 1]
                # Jeśli pierwsza linia nie jest ikoną, to prawdopodobnie nazwa kategorii
                elif i == 0 and line not in ["chevron_right", "expand_more"] and "PLN" not in line:
                    return line

            # Fallback - znajdź pierwszą linię, która nie jest ikoną ani ceną
            for line in lines:
                if line not in ["chevron_right", "expand_more"] and "PLN" not in line and ":" not in line:
                    return line

            return None

        except Exception as e:
            logger.debug(f"Błąd podczas wyodrębniania nazwy kategorii: {e}")
            return None

    def _node_has_children(self, node) -> bool:
        """
        Sprawdza czy węzeł ma dzieci (podkategorie).

        Args:
            node: Element Selenium reprezentujący węzeł

        Returns:
            True jeśli węzeł ma dzieci
        """
        try:
            # Sprawdź czy jest przycisk toggle
            toggle_button = node.find_elements(By.CSS_SELECTOR, "button[mattreenodetoggle]")
            return len(toggle_button) > 0
        except:
            return False

    def _could_be_parent(self, potential_parent, child) -> bool:
        """
        Sprawdza czy węzeł może być rodzicem innego węzła (heurystyka).

        Args:
            potential_parent: Potencjalny węzeł rodzic
            child: Węzeł dziecko

        Returns:
            True jeśli może być rodzicem
        """
        try:
            # Sprawdź pozycję w DOM - rodzic powinien być przed dzieckiem
            parent_location = potential_parent.location
            child_location = child.location

            # Rodzic powinien być wyżej (mniejszy y) lub na tym samym poziomie ale wcześniej (mniejszy x)
            if parent_location['y'] < child_location['y']:
                return True
            elif parent_location['y'] == child_location['y'] and parent_location['x'] < child_location['x']:
                return True

            return False
        except:
            return False

    def _set_parent_relationships(self, hierarchy: Dict[str, Dict], tree_nodes) -> None:
        """
        Ustala relacje parent-child w hierarchii - zoptymalizowana wersja.

        Args:
            hierarchy: Mapa hierarchii do aktualizacji
            tree_nodes: Lista węzłów drzewa
        """
        try:
            logger.info("Budowanie relacji parent-child...")

            # Sortuj węzły według pozycji w DOM i pogrupuj według poziomów
            nodes_by_level = {}
            nodes_with_position = []

            for i, node in enumerate(tree_nodes):
                node_id = node.get_attribute("data-cy")
                if node_id and node_id in hierarchy:
                    level = hierarchy[node_id]['level']
                    if level not in nodes_by_level:
                        nodes_by_level[level] = []

                    node_entry = (i, node_id, hierarchy[node_id])
                    nodes_by_level[level].append(node_entry)
                    nodes_with_position.append(node_entry)

            logger.info(f"Węzły pogrupowane według poziomów: {dict((k, len(v)) for k, v in nodes_by_level.items())}")

            # Dla każdego poziomu (od 2 wzwyż) znajdź rodziców
            for level in sorted(nodes_by_level.keys()):
                if level <= 1:
                    continue

                parent_level = level - 1
                if parent_level not in nodes_by_level:
                    continue

                logger.info(f"Przetwarzanie poziomu {level} ({len(nodes_by_level[level])} węzłów)")

                # Dla każdego węzła na tym poziomie znajdź rodzica
                for position, node_id, node_info in nodes_by_level[level]:
                    # Znajdź najbliższego rodzica (ostatni węzeł poziomu parent_level przed tym węzłem)
                    best_parent = None
                    best_parent_position = -1

                    for parent_position, parent_id, parent_info in nodes_by_level[parent_level]:
                        if parent_position < position and parent_position > best_parent_position:
                            best_parent = (parent_id, parent_info)
                            best_parent_position = parent_position

                    if best_parent:
                        parent_id, parent_info = best_parent
                        node_info['parent_id'] = parent_id
                        node_info['parent_name'] = parent_info['name']
                        node_info['main_category'] = parent_info['main_category'] or parent_info['name']
                        node_info['full_path'] = f"{parent_info['full_path']} > {node_info['name']}"

            # Policz dzieci dla każdego węzła - zoptymalizowane
            logger.info("Liczenie dzieci...")
            children_count = {}
            for node_id, node_info in hierarchy.items():
                parent_id = node_info.get('parent_id')
                if parent_id:
                    children_count[parent_id] = children_count.get(parent_id, 0) + 1

            for node_id, count in children_count.items():
                if node_id in hierarchy:
                    hierarchy[node_id]['children_count'] = count

            logger.info("Relacje parent-child zostały ustalone")

        except Exception as e:
            logger.error(f"Błąd podczas ustalania relacji parent-child: {e}")

    def _parse_tree_node_simple(self, node, level: int, category_paths: Dict[str, str]) -> List[Dict]:
        """
        Uproszczone parsowanie węzła drzewa bez budowania pełnej hierarchii.

        Args:
            node: Element Selenium reprezentujący węzeł drzewa
            level: Poziom węzła w hierarchii
            category_paths: Mapa ścieżek kategorii

        Returns:
            Lista słowników z danymi kategorii
        """
        try:
            node_id = node.get_attribute("data-cy")
            if not node_id:
                return []

            node_text = node.text.strip()
            if not node_text:
                return []

            # Wyodrębnij nazwę kategorii
            category_name = self._extract_category_name_from_node(node)
            if not category_name:
                return []

            # Buduj ścieżkę kategorii
            if level == 1:
                full_path = category_name
                main_category = category_name
                parent_category = ""
            else:
                # Dla wyższych poziomów spróbuj znaleźć rodzica
                parent_path = ""
                parent_name = ""
                main_category = category_name  # Fallback

                # Znajdź najbliższego rodzica z poprzednich poziomów
                for existing_node_id, existing_path in category_paths.items():
                    if existing_node_id != node_id:
                        # Sprawdź czy to może być rodzic (heurystyka)
                        existing_node = self.driver.find_element(By.CSS_SELECTOR, f"[data-node-id='{existing_node_id}']")
                        if existing_node and self._could_be_parent(existing_node, node):
                            parent_path = existing_path
                            parent_name = existing_path.split(" > ")[-1] if " > " in existing_path else existing_path
                            main_category = existing_path.split(" > ")[0] if " > " in existing_path else existing_path
                            break

                if parent_path:
                    full_path = f"{parent_path} > {category_name}"
                    parent_category = parent_name
                else:
                    full_path = category_name
                    parent_category = ""

            # Zapisz ścieżkę dla tego węzła
            category_paths[node_id] = full_path

            # Sprawdź czy ma dzieci
            has_children = self._node_has_children(node)

            # Znajdź linie z cenami
            lines = [line.strip() for line in node_text.split('\n') if line.strip()]
            price_lines = [line for line in lines if "PLN" in line]

            if not price_lines:
                return []

            # Parsuj linie z cenami
            records = []
            for price_line in price_lines:
                record = self._parse_price_line_simple(
                    full_path, main_category, parent_category,
                    has_children, price_line
                )
                if record:
                    records.append(record)

            return records

        except Exception as e:
            logger.debug(f"Błąd podczas uproszczonego parsowania węzła: {e}")
            return []

    def _parse_price_line_simple(self, full_path: str, main_category: str,
                                parent_category: str, has_children: bool,
                                price_line: str) -> Optional[Dict]:
        """
        Uproszczone parsowanie linii z ceną.

        Args:
            full_path: Pełna ścieżka kategorii
            main_category: Kategoria główna
            parent_category: Kategoria nadrzędna
            has_children: Czy kategoria ma dzieci
            price_line: Linia z ceną

        Returns:
            Słownik z danymi rekordu lub None
        """
        try:
            # Podziel na okres i cenę
            if ":" not in price_line:
                return None

            period_part, price_part = price_line.split(":", 1)
            period = period_part.strip()
            price_text = price_part.strip()

            # Wyodrębnij ceny
            min_price, max_price = self._extract_prices(price_text)

            if min_price is None:
                return None

            # Utwórz rekord
            record = {
                'pelna_sciezka_kategorii': full_path,
                'kategoria_glowna': main_category,
                'parent_kategorii': parent_category,
                'czy_kategoria_ostateczna': not has_children,
                'liczba_podkategorii': 1 if has_children else 0,  # Uproszczenie
                'okres_czasowy': period,
                'cena_minimalna_pln': min_price,
                'cena_maksymalna_pln': max_price if max_price is not None else min_price
            }

            return record

        except Exception as e:
            logger.debug(f"Błąd podczas uproszczonego parsowania linii cenowej '{price_line}': {e}")
            return None

    def export_to_csv(self, filename: str = 'ceneo_cpc_pricelist.csv') -> None:
        """
        Eksport danych do pliku CSV.

        Args:
            filename: Nazwa pliku CSV
        """
        try:
            if not self.data:
                logger.warning("Brak danych do eksportu")
                return

            # Tworzenie DataFrame
            df = pd.DataFrame(self.data)

            # Eksport do CSV
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"Dane zostały wyeksportowane do pliku: {filename}")

        except Exception as e:
            logger.error(f"Błąd podczas eksportu do CSV: {e}")
            raise

    def _parse_tree_node_with_hierarchy(self, node, hierarchy_map: Dict[str, Dict]) -> List[Dict]:
        """
        Parsowanie pojedynczego węzła drzewa z uwzględnieniem hierarchii.

        Args:
            node: Element Selenium reprezentujący węzeł drzewa
            hierarchy_map: Mapa hierarchii kategorii

        Returns:
            Lista słowników z danymi kategorii
        """
        try:
            node_id = node.get_attribute("data-cy")
            if not node_id or node_id not in hierarchy_map:
                return []

            node_info = hierarchy_map[node_id]
            node_text = node.text.strip()

            if not node_text:
                return []

            # Podziel tekst na linie
            lines = [line.strip() for line in node_text.split('\n') if line.strip()]

            # Znajdź linie z cenami (zawierają "PLN")
            price_lines = [line for line in lines if "PLN" in line]

            if not price_lines:
                return []

            # Parsuj linie z cenami
            records = []
            for price_line in price_lines:
                record = self._parse_price_line_with_hierarchy(node_info, price_line)
                if record:
                    records.append(record)

            return records

        except Exception as e:
            logger.debug(f"Błąd podczas parsowania węzła z hierarchią: {e}")
            return []

    def _parse_price_line_with_hierarchy(self, node_info: Dict, price_line: str) -> Optional[Dict]:
        """
        Parsowanie linii z ceną z uwzględnieniem hierarchii.

        Args:
            node_info: Informacje o węźle z mapy hierarchii
            price_line: Linia z ceną (np. "styczeń - marzec: 0,52 - 1,22 PLN")

        Returns:
            Słownik z danymi rekordu lub None
        """
        try:
            # Podziel na okres i cenę
            if ":" not in price_line:
                return None

            period_part, price_part = price_line.split(":", 1)
            period = period_part.strip()
            price_text = price_part.strip()

            # Wyodrębnij ceny
            min_price, max_price = self._extract_prices(price_text)

            if min_price is None:
                return None

            # Utwórz rekord z pełnymi informacjami o hierarchii
            record = {
                'pelna_sciezka_kategorii': node_info['full_path'],
                'kategoria_glowna': node_info['main_category'] or node_info['name'],
                'parent_kategorii': node_info['parent_name'],
                'czy_kategoria_ostateczna': not node_info['has_children'],
                'liczba_podkategorii': node_info.get('children_count', 0),
                'okres_czasowy': period,
                'cena_minimalna_pln': min_price,
                'cena_maksymalna_pln': max_price if max_price is not None else min_price
            }

            return record

        except Exception as e:
            logger.debug(f"Błąd podczas parsowania linii cenowej z hierarchią '{price_line}': {e}")
            return None

    def _extract_prices(self, price_text: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Wyodrębnienie cen z tekstu.

        Args:
            price_text: Tekst z ceną (np. "0,52 - 1,22 PLN" lub "1,22 PLN")

        Returns:
            Tuple (cena_min, cena_max) lub (None, None) jeśli błąd
        """
        try:
            import re

            # Usuń "PLN" i inne niepotrzebne znaki
            clean_text = price_text.replace("PLN", "").strip()

            # Znajdź wszystkie liczby z przecinkami
            price_pattern = r'\d+[,\.]\d+'
            prices = re.findall(price_pattern, clean_text)

            if not prices:
                return None, None

            # Konwertuj przecinki na kropki i zamień na float
            float_prices = []
            for price in prices:
                try:
                    float_price = float(price.replace(',', '.'))
                    float_prices.append(float_price)
                except ValueError:
                    continue

            if not float_prices:
                return None, None

            if len(float_prices) == 1:
                return float_prices[0], float_prices[0]
            else:
                return min(float_prices), max(float_prices)

        except Exception as e:
            logger.debug(f"Błąd podczas wyodrębniania cen z '{price_text}': {e}")
            return None, None
    
    def save_to_csv(self, filename: str = "ceneo_cpc_pricelist.csv") -> None:
        """
        Zapisanie danych do pliku CSV.
        
        Args:
            filename: Nazwa pliku CSV
        """
        try:
            if not self.data:
                logger.warning("Brak danych do zapisania")
                return
            
            df = pd.DataFrame(self.data)
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"Dane zostały zapisane do pliku: {filename}")
            logger.info(f"Liczba rekordów: {len(self.data)}")
            
        except Exception as e:
            logger.error(f"Błąd podczas zapisywania do CSV: {e}")
            raise
    
    def cleanup(self) -> None:
        """Zamknięcie przeglądarki i czyszczenie zasobów."""
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka została zamknięta")
    
    def run(self) -> None:
        """Główna metoda uruchamiająca cały proces scrapingu."""
        try:
            logger.info("Rozpoczęcie procesu scrapingu Ceneo CPC...")

            self.setup_driver()
            self.navigate_to_page()
            self.expand_all_categories()
            self.parse_html_content()
            # Nie wywołujemy save_to_csv() bo dane są już zapisane w _extract_data_with_selenium

            logger.info("Proces scrapingu zakończony pomyślnie!")

        except Exception as e:
            logger.error(f"Błąd podczas procesu scrapingu: {e}")
            raise
        finally:
            self.cleanup()

def main():
    """Funkcja główna."""
    scraper = CeneoCPCScraper(headless=False)  # Ustaw True dla trybu headless
    scraper.run()

if __name__ == "__main__":
    main()
