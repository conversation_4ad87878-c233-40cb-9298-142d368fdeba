#!/usr/bin/env python3
"""
Kompletny scraper prowizji <PERSON>o - gwarantuje rozwinięcie WSZYSTKICH węzłów.
"""

import logging
import csv
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CeneoCompleteProwizjeScraper:
    def __init__(self):
        self.driver = None
        self.csv_file = 'ceneo_prowizje_complete_data.csv'
        self.processed_nodes = set()
        self.expanded_nodes = set()  # Śledzenie rozwiniętych węzłów
        
    def setup_driver(self):
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Przeglądarka uruchomiona")

    def navigate_to_page(self):
        self.driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
        logger.info("Nawigacja do strony zakończona")
        
        try:
            WebDriverWait(self.driver, 30).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
            )
            logger.info("Splash screen zniknął")
        except:
            logger.info("Splash screen nie został wykryty")
        
        time.sleep(5)
        
        # Przełącz na zakładkę "Kup Teraz"
        kup_teraz_tab = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Kup Teraz')]"))
        )
        self.driver.execute_script("arguments[0].click();", kup_teraz_tab)
        logger.info("Przełączono na zakładkę 'Kup Teraz'")
        time.sleep(5)
        logger.info("Strona załadowana")

    def get_category_name(self, node):
        try:
            text = node.text.strip()
            if not text:
                return None
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            for line in lines:
                if line not in ['chevron_right', 'expand_more'] and '%' not in line and 'zł' not in line:
                    return line
            
            return None
        except Exception as e:
            logger.warning(f"Błąd pobierania nazwy kategorii: {e}")
            return None

    def get_prowizje_info(self, node):
        try:
            text = node.text.strip()
            if not text:
                return []
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            prowizje_data = []
            
            for line in lines:
                if line in ['chevron_right', 'expand_more']:
                    continue
                
                if '%' in line and ':' not in line and 'zł' not in line:
                    prowizja_line = line.strip()
                    
                    if ' - ' in prowizja_line:
                        parts = prowizja_line.split(' - ')
                        if len(parts) == 2:
                            min_prowizja = parts[0].strip().replace('%', '')
                            max_prowizja = parts[1].strip().replace('%', '')
                            prowizje_data.append({
                                'period': 'Stała prowizja',
                                'min_prowizja': min_prowizja,
                                'max_prowizja': max_prowizja
                            })
                    else:
                        single_prowizja = prowizja_line.replace('%', '').strip()
                        if single_prowizja:
                            prowizje_data.append({
                                'period': 'Stała prowizja',
                                'min_prowizja': single_prowizja,
                                'max_prowizja': single_prowizja
                            })
            
            return prowizje_data
        except Exception as e:
            logger.warning(f"Błąd parsowania prowizji: {e}")
            return []

    def has_expand_button(self, node):
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return True
            return False
        except:
            return False

    def get_expand_button(self, node):
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return icon
            return None
        except:
            return None

    def build_category_path_safe(self, current_node, level, all_nodes):
        try:
            current_name = self.get_category_name(current_node)
            if not current_name:
                return "Nieznana kategoria"
            
            if level == 1:
                return current_name
            
            try:
                current_index = all_nodes.index(current_node)
            except ValueError:
                return current_name
            
            for i in range(current_index - 1, -1, -1):
                try:
                    node = all_nodes[i]
                    node_level_str = node.get_attribute('aria-level')
                    if not node_level_str:
                        continue
                        
                    node_level = int(node_level_str)
                    if node_level == level - 1:
                        parent_name = self.get_category_name(node)
                        if parent_name:
                            parent_path = self.build_category_path_safe(node, node_level, all_nodes)
                            return f"{parent_path} > {current_name}"
                        break
                except Exception as e:
                    logger.warning(f"Błąd przy budowaniu ścieżki dla węzła {i}: {e}")
                    continue
            
            return current_name
            
        except Exception as e:
            logger.warning(f"Błąd budowania ścieżki: {e}")
            return f"Błąd ścieżki - {self.get_category_name(current_node) or 'Nieznana'}"

    def get_node_signature(self, node, all_nodes):
        try:
            position = all_nodes.index(node)
            level = node.get_attribute('aria-level') or '0'
            name = self.get_category_name(node) or 'unknown'
            return f"{level}_{position}_{name[:20]}"
        except:
            return f"unknown_{id(node)}"

    def get_expand_signature(self, node, all_nodes):
        """Unikalny podpis dla śledzenia rozwiniętych węzłów."""
        try:
            level = node.get_attribute('aria-level') or '0'
            name = self.get_category_name(node) or 'unknown'
            return f"{level}_{name}"
        except:
            return f"unknown_{id(node)}"

    def save_category_record(self, writer, node, all_nodes, record_count):
        try:
            level_str = node.get_attribute('aria-level')
            if not level_str:
                return record_count
                
            level = int(level_str)
            category_name = self.get_category_name(node)
            
            if not category_name:
                return record_count
            
            node_signature = self.get_node_signature(node, all_nodes)
            if node_signature in self.processed_nodes:
                return record_count
            
            self.processed_nodes.add(node_signature)
            
            full_path = self.build_category_path_safe(node, level, all_nodes)
            main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
            parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
            final_category_name = category_name
            
            has_children = self.has_expand_button(node)
            children_count = 0
            
            prowizje_data = self.get_prowizje_info(node)
            
            if prowizje_data:
                for prowizja_info in prowizje_data:
                    record = {
                        'pelna_sciezka_kategorii': full_path,
                        'kategoria_glowna': main_category,
                        'parent_kategorii': parent_category,
                        'nazwa_kategorii_ostatecznej': final_category_name,
                        'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                        'liczba_podkategorii': children_count,
                        'okres_czasowy': prowizja_info['period'],
                        'prowizja_minimalna_procent': prowizja_info['min_prowizja'],
                        'prowizja_maksymalna_procent': prowizja_info['max_prowizja']
                    }
                    writer.writerow(record)
                    record_count += 1
            else:
                record = {
                    'pelna_sciezka_kategorii': full_path,
                    'kategoria_glowna': main_category,
                    'parent_kategorii': parent_category,
                    'nazwa_kategorii_ostatecznej': final_category_name,
                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                    'liczba_podkategorii': children_count,
                    'okres_czasowy': 'Brak danych prowizyjnych',
                    'prowizja_minimalna_procent': 'N/A',
                    'prowizja_maksymalna_procent': 'N/A'
                }
                writer.writerow(record)
                record_count += 1
            
            return record_count
            
        except Exception as e:
            logger.warning(f"Błąd zapisywania rekordu: {e}")
            return record_count

    def expand_all_nodes_aggressively(self):
        """Agresywnie rozwija WSZYSTKIE możliwe węzły."""
        logger.info("Rozpoczęcie agresywnego rozwijania wszystkich węzłów...")
        
        iteration = 0
        max_iterations = 20  # Zwiększona liczba iteracji
        
        while iteration < max_iterations:
            iteration += 1
            logger.info(f"=== ITERACJA ROZWIJANIA {iteration} ===")
            
            try:
                # Znajdź wszystkie węzły do rozwinięcia
                all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                nodes_to_expand = []
                
                for node in all_nodes:
                    if self.has_expand_button(node):
                        expand_signature = self.get_expand_signature(node, all_nodes)
                        if expand_signature not in self.expanded_nodes:
                            nodes_to_expand.append((node, expand_signature))
                
                logger.info(f"Znaleziono {len(nodes_to_expand)} nowych węzłów do rozwinięcia")
                
                if not nodes_to_expand:
                    logger.info("Brak nowych węzłów do rozwinięcia - koniec rozwijania")
                    break
                
                # Rozwiń WSZYSTKIE znalezione węzły (nie tylko 50)
                expanded_count = 0
                for node, signature in nodes_to_expand:
                    try:
                        expand_button = self.get_expand_button(node)
                        if expand_button:
                            # Sprawdź czy przycisk jest nadal aktywny
                            if expand_button.text == 'chevron_right':
                                self.driver.execute_script("arguments[0].click();", expand_button)
                                self.expanded_nodes.add(signature)
                                expanded_count += 1
                                time.sleep(0.3)
                                
                                if expanded_count % 20 == 0:
                                    logger.info(f"Rozwinięto {expanded_count}/{len(nodes_to_expand)} węzłów...")
                                    time.sleep(2)
                                    
                    except Exception as e:
                        logger.warning(f"Błąd rozwijania węzła: {e}")
                        continue
                
                logger.info(f"Rozwinięto {expanded_count} węzłów w iteracji {iteration}")
                time.sleep(5)  # Pauza na załadowanie nowych węzłów
                
            except Exception as e:
                logger.error(f"Błąd w iteracji rozwijania {iteration}: {e}")
                time.sleep(10)
                continue
        
        # Finalne sprawdzenie
        final_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
        expandable_nodes = [n for n in final_nodes if self.has_expand_button(n)]
        
        logger.info(f"Po {iteration} iteracjach:")
        logger.info(f"- Łączna liczba węzłów: {len(final_nodes)}")
        logger.info(f"- Węzły nadal do rozwinięcia: {len(expandable_nodes)}")
        logger.info(f"- Rozwinięte węzły: {len(self.expanded_nodes)}")

    def run_complete_scraping(self):
        """Uruchamia kompletny scraping z pełnym rozwijaniem."""
        logger.info("Rozpoczęcie kompletnego scrapingu prowizji...")
        
        # Najpierw rozwiń WSZYSTKIE węzły
        self.expand_all_nodes_aggressively()
        
        # Następnie zapisz wszystkie kategorie
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
            fieldnames = [
                'pelna_sciezka_kategorii', 'kategoria_glowna', 'parent_kategorii',
                'nazwa_kategorii_ostatecznej', 'czy_kategoria_ostateczna', 'liczba_podkategorii', 
                'okres_czasowy', 'prowizja_minimalna_procent', 'prowizja_maksymalna_procent'
            ]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            
            record_count = 0
            
            # Pobierz wszystkie węzły po rozwinięciu
            all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            logger.info(f"Zapisywanie {len(all_nodes)} węzłów...")
            
            for i, node in enumerate(all_nodes):
                try:
                    record_count = self.save_category_record(writer, node, all_nodes, record_count)
                    
                    if record_count % 200 == 0 and record_count > 0:
                        logger.info(f"Zapisano {record_count} rekordów... ({i+1}/{len(all_nodes)} węzłów)")
                        file.flush()
                        
                except Exception as e:
                    logger.warning(f"Błąd przetwarzania węzła {i}: {e}")
                    continue
            
            logger.info(f"Zapisano łącznie {record_count} rekordów do {self.csv_file}")

    def cleanup(self):
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka zamknięta")

    def run(self):
        try:
            logger.info("=== ROZPOCZĘCIE KOMPLETNEGO SCRAPINGU PROWIZJI ===")
            
            self.setup_driver()
            self.navigate_to_page()
            self.run_complete_scraping()
            
            logger.info("=== KOMPLETNY SCRAPING PROWIZJI ZAKOŃCZONY POMYŚLNIE ===")
            
        except Exception as e:
            logger.error(f"Błąd podczas kompletnego scrapingu prowizji: {e}")
        finally:
            self.cleanup()

def main():
    scraper = CeneoCompleteProwizjeScraper()
    scraper.run()

if __name__ == "__main__":
    main()
