#!/usr/bin/env python3
"""
Scraper <PERSON><PERSON><PERSON> "<PERSON><PERSON>" - roz<PERSON>ja kategorie i zapisuje prowizje bezpośrednio do CSV.
"""

import logging
import csv
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CeneoProwizjeScraper:
    def __init__(self):
        self.driver = None
        self.csv_file = 'ceneo_prowizje_data.csv'
        self.processed_categories = set()
        
    def setup_driver(self):
        """Konfiguracja przeglądarki Chrome."""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Przeglądarka uruchomiona")

    def navigate_to_page(self):
        """Nawigacja do strony Ceneo i przełączenie na zakładkę Kup Teraz."""
        self.driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
        logger.info("Nawigacja do strony zakończona")
        
        # Czeka na załadowanie
        try:
            WebDriverWait(self.driver, 30).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
            )
            logger.info("Splash screen zniknął")
        except:
            logger.info("Splash screen nie został wykryty")
        
        time.sleep(5)
        
        # Przełącz na zakładkę "Kup Teraz"
        try:
            kup_teraz_tab = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Kup Teraz')]"))
            )
            self.driver.execute_script("arguments[0].click();", kup_teraz_tab)
            logger.info("Przełączono na zakładkę 'Kup Teraz'")
            time.sleep(5)
        except Exception as e:
            logger.error(f"Nie udało się przełączyć na zakładkę 'Kup Teraz': {e}")
            # Spróbuj alternatywny selektor
            try:
                kup_teraz_tab = self.driver.find_element(By.XPATH, "//a[contains(@href, 'BuyNow')]")
                self.driver.execute_script("arguments[0].click();", kup_teraz_tab)
                logger.info("Przełączono na zakładkę 'Kup Teraz' (alternatywny selektor)")
                time.sleep(5)
            except Exception as e2:
                logger.error(f"Nie udało się przełączyć na zakładkę (alternatywny): {e2}")
        
        logger.info("Strona załadowana")

    def get_category_name(self, node):
        """Wyciąga nazwę kategorii z węzła."""
        try:
            text = node.text.strip()
            if not text:
                return None
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            # Znajdź pierwszą linię która nie jest ikoną ani prowizją
            for line in lines:
                if line not in ['chevron_right', 'expand_more'] and '%' not in line:
                    return line
            
            return None
        except:
            return None

    def get_prowizje_info(self, node):
        """Wyciąga informacje o prowizjach z węzła."""
        try:
            text = node.text.strip()
            if not text:
                return []

            lines = [line.strip() for line in text.split('\n') if line.strip()]
            prowizje_data = []

            # Szukaj linii z procentami (format: "2.3% - 19.5%")
            for line in lines:
                # Pomiń ikony i nazwy kategorii
                if line in ['chevron_right', 'expand_more']:
                    continue

                # Szukaj linii z prowizjami procentowymi (zawierają % ale nie :)
                if '%' in line and ':' not in line and 'zł' not in line:
                    # Format: "2.3% - 19.5%" lub "5.5%"
                    prowizja_line = line.strip()

                    if ' - ' in prowizja_line:
                        # Zakres prowizji: min% - max%
                        parts = prowizja_line.split(' - ')
                        if len(parts) == 2:
                            min_prowizja = parts[0].strip().replace('%', '')
                            max_prowizja = parts[1].strip().replace('%', '')
                            prowizje_data.append({
                                'period': 'Stała prowizja',  # Brak okresów w prowizjach
                                'min_prowizja': min_prowizja,
                                'max_prowizja': max_prowizja
                            })
                    else:
                        # Pojedyncza prowizja
                        single_prowizja = prowizja_line.replace('%', '').strip()
                        if single_prowizja:
                            prowizje_data.append({
                                'period': 'Stała prowizja',
                                'min_prowizja': single_prowizja,
                                'max_prowizja': single_prowizja
                            })

            return prowizje_data
        except Exception as e:
            logger.warning(f"Błąd parsowania prowizji: {e}")
            return []

    def has_expand_button(self, node):
        """Sprawdza czy węzeł ma przycisk rozwijania."""
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return True
            return False
        except:
            return False

    def get_expand_button(self, node):
        """Zwraca przycisk rozwijania dla węzła."""
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return icon
            return None
        except:
            return None

    def build_category_path(self, current_node, level):
        """Buduje pełną ścieżkę kategorii."""
        try:
            current_name = self.get_category_name(current_node)
            
            if level == 1:
                return current_name if current_name else "Nieznana kategoria"
            
            # Dla wyższych poziomów, znajdź rodzica
            all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            current_index = all_nodes.index(current_node)
            
            # Znajdź najbliższy węzeł z niższym poziomem przed tym węzłem
            for i in range(current_index - 1, -1, -1):
                node = all_nodes[i]
                node_level = int(node.get_attribute('aria-level'))
                if node_level == level - 1:
                    parent_name = self.get_category_name(node)
                    if parent_name:
                        parent_path = self.build_category_path(node, node_level)
                        return f"{parent_path} > {current_name}" if current_name else parent_path
                    break
            
            return current_name if current_name else "Nieznana kategoria"
        except:
            return "Błąd ścieżki"

    def process_and_save_categories(self):
        """Przetwarza kategorie i zapisuje prowizje do CSV."""
        logger.info("Rozpoczęcie przetwarzania kategorii prowizyjnych...")
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
            fieldnames = [
                'pelna_sciezka_kategorii', 'kategoria_glowna', 'parent_kategorii',
                'nazwa_kategorii_ostatecznej', 'czy_kategoria_ostateczna', 'liczba_podkategorii', 
                'okres_czasowy', 'prowizja_minimalna_procent', 'prowizja_maksymalna_procent'
            ]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            
            record_count = 0
            iteration = 0
            
            while True:
                iteration += 1
                logger.info(f"=== ITERACJA {iteration} ===")
                
                # Znajdź wszystkie węzły
                all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                logger.info(f"Znaleziono {len(all_nodes)} węzłów")
                
                # Znajdź węzły do rozwinięcia
                nodes_to_expand = []
                for node in all_nodes:
                    if self.has_expand_button(node):
                        # Użyj pozycji węzła jako identyfikatora zamiast data-cy (które może być None)
                        try:
                            node_position = all_nodes.index(node)
                            node_level = node.get_attribute('aria-level')
                            category_name = self.get_category_name(node)
                            node_id = f"level_{node_level}_pos_{node_position}_{category_name}"

                            if node_id not in self.processed_categories:
                                nodes_to_expand.append(node)
                        except:
                            continue
                
                logger.info(f"Węzłów do rozwinięcia: {len(nodes_to_expand)}")
                
                # Jeśli nie ma węzłów do rozwinięcia, przetwórz wszystkie i zakończ
                if not nodes_to_expand:
                    logger.info("Brak węzłów do rozwinięcia - przetwarzanie wszystkich kategorii")
                    
                    for node in all_nodes:
                        try:
                            # Użyj pozycji węzła jako identyfikatora
                            node_position = all_nodes.index(node)
                            node_level = node.get_attribute('aria-level')
                            category_name = self.get_category_name(node)
                            node_id = f"level_{node_level}_pos_{node_position}_{category_name}"

                            if node_id in self.processed_categories:
                                continue
                                
                            level = int(node.get_attribute('aria-level'))
                            category_name = self.get_category_name(node)
                            
                            if not category_name:
                                continue
                            
                            full_path = self.build_category_path(node, level)
                            main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
                            parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
                            final_category_name = category_name
                            
                            has_children = self.has_expand_button(node)
                            children_count = 0
                            
                            prowizje_data = self.get_prowizje_info(node)
                            
                            # Jeśli brak danych prowizyjnych, poczekaj i spróbuj ponownie
                            if not prowizje_data:
                                time.sleep(2)
                                prowizje_data = self.get_prowizje_info(node)
                            
                            if prowizje_data:
                                for prowizja_info in prowizje_data:
                                    record = {
                                        'pelna_sciezka_kategorii': full_path,
                                        'kategoria_glowna': main_category,
                                        'parent_kategorii': parent_category,
                                        'nazwa_kategorii_ostatecznej': final_category_name,
                                        'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                        'liczba_podkategorii': children_count,
                                        'okres_czasowy': prowizja_info['period'],
                                        'prowizja_minimalna_procent': prowizja_info['min_prowizja'],
                                        'prowizja_maksymalna_procent': prowizja_info['max_prowizja']
                                    }
                                    writer.writerow(record)
                                    record_count += 1
                            else:
                                record = {
                                    'pelna_sciezka_kategorii': full_path,
                                    'kategoria_glowna': main_category,
                                    'parent_kategorii': parent_category,
                                    'nazwa_kategorii_ostatecznej': final_category_name,
                                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                    'liczba_podkategorii': children_count,
                                    'okres_czasowy': 'Brak danych prowizyjnych',
                                    'prowizja_minimalna_procent': 'N/A',
                                    'prowizja_maksymalna_procent': 'N/A'
                                }
                                writer.writerow(record)
                                record_count += 1
                            
                            self.processed_categories.add(node_id)
                            
                            if record_count % 50 == 0:
                                logger.info(f"Zapisano {record_count} rekordów...")
                                file.flush()
                                
                        except Exception as e:
                            logger.warning(f"Błąd przetwarzania węzła: {e}")
                    
                    break
                
                # Przetwórz i zapisz obecne węzły PRZED rozwijaniem
                current_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                for node in current_nodes:
                    try:
                        # Użyj pozycji węzła jako identyfikatora
                        node_position = current_nodes.index(node)
                        node_level = node.get_attribute('aria-level')
                        category_name = self.get_category_name(node)
                        node_id = f"level_{node_level}_pos_{node_position}_{category_name}"

                        if node_id in self.processed_categories:
                            continue
                            
                        level = int(node.get_attribute('aria-level'))
                        category_name = self.get_category_name(node)
                        
                        if not category_name:
                            continue
                        
                        full_path = self.build_category_path(node, level)
                        main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
                        parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
                        final_category_name = category_name
                        
                        has_children = self.has_expand_button(node)
                        children_count = 0
                        
                        prowizje_data = self.get_prowizje_info(node)
                        
                        # Jeśli brak danych prowizyjnych, poczekaj i spróbuj ponownie
                        if not prowizje_data:
                            time.sleep(2)
                            prowizje_data = self.get_prowizje_info(node)
                        
                        if prowizje_data:
                            for prowizja_info in prowizje_data:
                                record = {
                                    'pelna_sciezka_kategorii': full_path,
                                    'kategoria_glowna': main_category,
                                    'parent_kategorii': parent_category,
                                    'nazwa_kategorii_ostatecznej': final_category_name,
                                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                    'liczba_podkategorii': children_count,
                                    'okres_czasowy': prowizja_info['period'],
                                    'prowizja_minimalna_procent': prowizja_info['min_prowizja'],
                                    'prowizja_maksymalna_procent': prowizja_info['max_prowizja']
                                }
                                writer.writerow(record)
                                record_count += 1
                        else:
                            record = {
                                'pelna_sciezka_kategorii': full_path,
                                'kategoria_glowna': main_category,
                                'parent_kategorii': parent_category,
                                'nazwa_kategorii_ostatecznej': final_category_name,
                                'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                'liczba_podkategorii': children_count,
                                'okres_czasowy': 'Brak danych prowizyjnych',
                                'prowizja_minimalna_procent': 'N/A',
                                'prowizja_maksymalna_procent': 'N/A'
                            }
                            writer.writerow(record)
                            record_count += 1
                        
                        self.processed_categories.add(node_id)
                        
                        if record_count % 10 == 0:
                            logger.info(f"Zapisano {record_count} rekordów...")
                            file.flush()
                            
                    except Exception as e:
                        logger.warning(f"Błąd przetwarzania węzła: {e}")
                
                # Rozwija WSZYSTKIE węzły w tej iteracji (nie tylko 10)
                expanded_count = 0
                for node in nodes_to_expand:
                    try:
                        expand_button = self.get_expand_button(node)
                        if expand_button:
                            self.driver.execute_script("arguments[0].click();", expand_button)
                            expanded_count += 1
                            time.sleep(0.3)

                            # Co 20 rozwinięć zrób dłuższą pauzę
                            if expanded_count % 20 == 0:
                                logger.info(f"Rozwinięto {expanded_count} węzłów...")
                                time.sleep(2)

                    except Exception as e:
                        logger.warning(f"Błąd rozwijania węzła: {e}")

                logger.info(f"Rozwinięto {expanded_count} węzłów")
                time.sleep(3)
            
            logger.info(f"Zapisano łącznie {record_count} rekordów do {self.csv_file}")

    def cleanup(self):
        """Zamknięcie przeglądarki."""
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka zamknięta")

    def run(self):
        """Uruchamia scraper prowizji."""
        try:
            logger.info("=== ROZPOCZĘCIE SCRAPINGU PROWIZJI ===")
            
            self.setup_driver()
            self.navigate_to_page()
            self.process_and_save_categories()
            
            logger.info("=== SCRAPING PROWIZJI ZAKOŃCZONY POMYŚLNIE ===")
            
        except Exception as e:
            logger.error(f"Błąd podczas scrapingu prowizji: {e}")
        finally:
            self.cleanup()

def main():
    """Funkcja główna."""
    scraper = CeneoProwizjeScraper()
    scraper.run()

if __name__ == "__main__":
    main()
