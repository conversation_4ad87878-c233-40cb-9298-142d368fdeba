#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard Analizy Sezonowości Kategorii
Interaktywny dashboard do analizy sezonowości kategorii produktów
z wykorzystaniem danych CPC i prowizji "Kup Teraz"
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, dash_table, Input, Output, callback
import json
from datetime import datetime, timedelta
import numpy as np

# Konfiguracja aplikacji Dash
app = dash.Dash(__name__)
app.title = "Dashboard Analizy Sezonowości Kategorii"

# Funkcja do wczytania i przetworzenia danych
def load_and_process_data():
    """Wczytuje i przetwarza dane z pliku CSV"""
    try:
        # Wczytanie danych z pliku CSV
        df = pd.read_csv('report/CENEO_CPC_COMMISION_MODEL.csv')
        
        # Lista do przechowywania przetworzonych danych
        processed_data = []
        
        for _, row in df.iterrows():
            try:
                # Parsowanie JSON z analizy cen
                analiza_json = json.loads(row['analiza_cen_w_okresach'])
                
                for okres_data in analiza_json['analiza_cen_w_okresach']:
                    processed_row = {
                        'KategoriaGlowna': row['kategoria_glowna'],
                        'KategoriaLisc': row['leaf_category'],
                        'PelnaSciezka': row['pelna_sciezka'],
                        'MinProwizjaProc': row['min_commision_perc'] if pd.notna(row['min_commision_perc']) else 0,
                        'MaxProwizjaProc': row['max_commision_perc'] if pd.notna(row['max_commision_perc']) else 0,
                        'CenaBazowa': float(row['cena_bazowa_kategorii_pln']),
                        'NazwaOkresu': okres_data['okres'],
                        'TypSezonowosci': okres_data['typ_sezonowosci'],
                        'CenaWOkresie': float(okres_data['cena_w_okresie']),
                        'RoznicaBezwzgledna': float(okres_data['roznica_bezwzgledna_pln']),
                        'RoznicaProcentowa': float(okres_data['roznica_procentowa']) * 100,
                        'CzyOkresBazowy': 'TAK' if okres_data['typ_sezonowosci'] == 'Okres bazowy' else 'NIE'
                    }
                    processed_data.append(processed_row)
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Błąd przetwarzania wiersza: {e}")
                continue
        
        return pd.DataFrame(processed_data)
    
    except FileNotFoundError:
        # Jeśli plik nie istnieje, generujemy przykładowe dane
        return generate_sample_data()

def generate_sample_data():
    """Generuje przykładowe dane do testowania aplikacji"""
    sample_data = []
    
    # Definicja kategorii głównych i ich okresów sezonowych
    categories_data = {
        'Komputery': [
            {'okres': 'styczeń - kwiecień', 'typ': 'Okres bazowy', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'maj', 'typ': 'Okazje Wiosenne', 'cena': 1.23, 'roznica': 0.171},
            {'okres': 'czerwiec - wrzesień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.23, 'roznica': 0.171}
        ],
        'Sprzęt AGD': [
            {'okres': 'styczeń - maj', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'czerwiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.44, 'roznica': 0.143},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Sprzęt RTV': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Motoryzacja': [
            {'okres': 'styczeń', 'typ': 'Wyprzedaże Noworoczne', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'luty - czerwiec', 'typ': 'Okres bazowy', 'cena': 1.47, 'roznica': 0.0},
            {'okres': 'lipiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.36, 'roznica': -0.075},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.44, 'roznica': -0.02}
        ],
        'Outlet': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.28, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.49, 'roznica': 0.164}
        ]
    }
    
    for kategoria, okresy in categories_data.items():
        for okres_data in okresy:
            sample_data.append({
                'KategoriaGlowna': kategoria,
                'KategoriaLisc': kategoria,
                'PelnaSciezka': kategoria,
                'MinProwizjaProc': np.random.uniform(1.0, 3.0),
                'MaxProwizjaProc': np.random.uniform(3.0, 8.0),
                'CenaBazowa': np.random.uniform(0.8, 1.8),
                'NazwaOkresu': okres_data['okres'],
                'TypSezonowosci': okres_data['typ'],
                'CenaWOkresie': okres_data['cena'],
                'RoznicaBezwzgledna': okres_data['cena'] - 1.0,
                'RoznicaProcentowa': okres_data['roznica'] * 100,
                'CzyOkresBazowy': 'TAK' if okres_data['typ'] == 'Okres bazowy' else 'NIE'
            })
    
    return pd.DataFrame(sample_data)

# Wczytanie danych
df = load_and_process_data()

# Funkcje pomocnicze do tworzenia dat dla wykresu Gantta
def create_gantt_dates(okres_nazwa):
    """Tworzy daty początkowe i końcowe dla okresów sezonowych"""
    current_year = datetime.now().year
    
    # Mapowanie nazw miesięcy na numery
    months_map = {
        'styczeń': 1, 'luty': 2, 'marzec': 3, 'kwiecień': 4,
        'maj': 5, 'czerwiec': 6, 'lipiec': 7, 'sierpień': 8,
        'wrzesień': 9, 'październik': 10, 'listopad': 11, 'grudzień': 12
    }
    
    try:
        if ' - ' in okres_nazwa:
            start_month, end_month = okres_nazwa.split(' - ')
            start_num = months_map.get(start_month.strip(), 1)
            end_num = months_map.get(end_month.strip(), 12)
        else:
            start_num = end_num = months_map.get(okres_nazwa.strip(), 1)
        
        start_date = datetime(current_year, start_num, 1)
        
        # Obliczenie ostatniego dnia miesiąca końcowego
        if end_num == 12:
            end_date = datetime(current_year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(current_year, end_num + 1, 1) - timedelta(days=1)
        
        return start_date, end_date
    
    except:
        # Domyślne daty w przypadku błędu
        return datetime(current_year, 1, 1), datetime(current_year, 12, 31)

# Layout aplikacji
app.layout = html.Div([
    # Nagłówek
    html.Div([
        html.H1("Dashboard Analizy Sezonowości Kategorii", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'})
    ]),
    
    # Filtr główny
    html.Div([
        html.Label("Wybierz główną kategorię:", 
                  style={'fontSize': '16px', 'fontWeight': 'bold', 'marginBottom': '10px'}),
        dcc.Dropdown(
            id='kategoria-dropdown',
            options=[{'label': kat, 'value': kat} for kat in sorted(df['KategoriaGlowna'].unique())],
            value=sorted(df['KategoriaGlowna'].unique())[0],
            style={'marginBottom': '30px'}
        )
    ], style={'margin': '20px'}),
    
    # Wykres Gantta - Oś Czasu Sezonowości
    html.Div([
        html.H2("Oś Czasu Sezonowości", 
                style={'color': '#34495e', 'marginBottom': '20px'}),
        dcc.Graph(id='gantt-chart')
    ], style={'margin': '20px', 'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'}),
    
    # Tabela Porównawcza
    html.Div([
        html.H2("Analiza Cen i Prowizji w Okresach", 
                style={'color': '#34495e', 'marginBottom': '20px'}),
        dash_table.DataTable(
            id='comparison-table',
            columns=[
                {'name': 'Nazwa Okresu', 'id': 'NazwaOkresu'},
                {'name': 'Typ Sezonowości', 'id': 'TypSezonowosci'},
                {'name': 'Zakres Prowizji (%)', 'id': 'ZakresProwizji'},
                {'name': 'Cena w Okresie (PLN)', 'id': 'CenaWOkresie', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                {'name': 'Cena vs Okres Bazowy', 'id': 'CenaVsBazowy'}
            ],
            style_cell={'textAlign': 'left', 'padding': '10px'},
            style_header={'backgroundColor': '#3498db', 'color': 'white', 'fontWeight': 'bold'},
            style_data_conditional=[
                {
                    'if': {'filter_query': '{CenaVsBazowy} contains "+"'},
                    'backgroundColor': '#d5f4e6',
                    'color': '#27ae60'
                },
                {
                    'if': {'filter_query': '{CenaVsBazowy} contains "-"'},
                    'backgroundColor': '#fadbd8',
                    'color': '#e74c3c'
                }
            ]
        )
    ], style={'margin': '20px', 'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'})
])

# Callback dla aktualizacji komponentów
@app.callback(
    [Output('gantt-chart', 'figure'),
     Output('comparison-table', 'data')],
    [Input('kategoria-dropdown', 'value')]
)
def update_dashboard(selected_category):
    """Aktualizuje wykres Gantta i tabelę na podstawie wybranej kategorii"""
    
    # Filtrowanie danych dla wybranej kategorii
    filtered_df = df[df['KategoriaGlowna'] == selected_category].copy()
    
    if filtered_df.empty:
        # Zwróć puste komponenty jeśli brak danych
        empty_fig = go.Figure()
        empty_fig.update_layout(title="Brak danych dla wybranej kategorii")
        return empty_fig, []
    
    # Przygotowanie danych dla wykresu Gantta
    gantt_data = []
    for _, row in filtered_df.iterrows():
        start_date, end_date = create_gantt_dates(row['NazwaOkresu'])
        
        gantt_data.append({
            'Task': row['NazwaOkresu'],
            'Start': start_date,
            'Finish': end_date,
            'Resource': row['TypSezonowosci'],
            'CzyBazowy': row['CzyOkresBazowy']
        })
    
    gantt_df = pd.DataFrame(gantt_data)
    
    # Tworzenie wykresu Gantta
    colors = {'TAK': '#3498db', 'NIE': '#e74c3c'}  # Niebieski dla bazowego, czerwony dla pików
    
    fig = px.timeline(
        gantt_df, 
        x_start="Start", 
        x_end="Finish", 
        y="Task",
        color="CzyBazowy",
        color_discrete_map=colors,
        title=f"Okresy Sezonowe dla Kategorii: {selected_category}",
        hover_data=["Resource"]
    )
    
    fig.update_layout(
        height=400,
        xaxis_title="Okres",
        yaxis_title="Nazwa Okresu",
        legend_title="Typ Okresu",
        font=dict(size=12)
    )
    
    # Przygotowanie danych dla tabeli
    table_data = []
    
    # Znajdź cenę bazową (okres bazowy)
    bazowy_okres = filtered_df[filtered_df['CzyOkresBazowy'] == 'TAK']
    cena_bazowa = bazowy_okres['CenaWOkresie'].iloc[0] if not bazowy_okres.empty else 1.0
    
    for _, row in filtered_df.iterrows():
        # Obliczenie zakresu prowizji
        min_prow = row['MinProwizjaProc']
        max_prow = row['MaxProwizjaProc']
        
        if min_prow == max_prow:
            zakres_prowizji = f"{min_prow:.1f}%" if min_prow > 0 else "Brak danych"
        else:
            zakres_prowizji = f"{min_prow:.1f}% - {max_prow:.1f}%" if min_prow > 0 or max_prow > 0 else "Brak danych"
        
        # Obliczenie różnicy względem okresu bazowego
        if row['CzyOkresBazowy'] == 'TAK':
            cena_vs_bazowy = "Okres bazowy"
        else:
            roznica_proc = ((row['CenaWOkresie'] - cena_bazowa) / cena_bazowa) * 100
            if roznica_proc > 0:
                cena_vs_bazowy = f"+{roznica_proc:.1f}%"
            else:
                cena_vs_bazowy = f"{roznica_proc:.1f}%"
        
        table_data.append({
            'NazwaOkresu': row['NazwaOkresu'],
            'TypSezonowosci': row['TypSezonowosci'],
            'ZakresProwizji': zakres_prowizji,
            'CenaWOkresie': row['CenaWOkresie'],
            'CenaVsBazowy': cena_vs_bazowy
        })
    
    return fig, table_data

# Uruchomienie aplikacji
if __name__ == '__main__':
    print("Uruchamianie Dashboard Analizy Sezonowości Kategorii...")
    print("Aplikacja będzie dostępna pod adresem: http://127.0.0.1:8050/")
    app.run(debug=True, host='127.0.0.1', port=8050)
