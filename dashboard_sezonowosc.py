#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard Analizy Sezonowości Kategorii
Interaktywny dashboard do analizy sezonowości kategorii produktów
z wykorzystaniem danych CPC i prowizji "Kup Teraz"
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, dash_table, Input, Output, callback
import json
from datetime import datetime, timedelta
import numpy as np

# Konfiguracja aplikacji Dash
external_stylesheets = ['https://codepen.io/chriddyp/pen/bWLwgP.css']
app = dash.Dash(__name__, external_stylesheets=external_stylesheets)
app.title = "Dashboard Analizy Sezonowości Kategorii"

# Funkcja do wczytania i przetworzenia danych
def load_and_process_data():
    """Wczytuje i przetwarza dane z pliku CSV"""
    try:
        # Wczytanie danych z pliku CSV
        df = pd.read_csv('report/CENEO_CPC_COMMISION_MODEL.csv')
        
        # Lista do przechowywania przetworzonych danych
        processed_data = []
        
        for _, row in df.iterrows():
            try:
                # Parsowanie JSON z analizy cen
                analiza_json = json.loads(row['analiza_cen_w_okresach'])
                
                for okres_data in analiza_json['analiza_cen_w_okresach']:
                    processed_row = {
                        'KategoriaGlowna': row['kategoria_glowna'],
                        'KategoriaLisc': row['leaf_category'],
                        'PelnaSciezka': row['pelna_sciezka'],
                        'MinProwizjaProc': row['min_commision_perc'] if pd.notna(row['min_commision_perc']) else 0,
                        'MaxProwizjaProc': row['max_commision_perc'] if pd.notna(row['max_commision_perc']) else 0,
                        'CenaBazowa': float(row['cena_bazowa_kategorii_pln']),
                        'NazwaOkresu': okres_data['okres'],
                        'TypSezonowosci': okres_data['typ_sezonowosci'],
                        'CenaWOkresie': float(okres_data['cena_w_okresie']),
                        'RoznicaBezwzgledna': float(okres_data['roznica_bezwzgledna_pln']),
                        'RoznicaProcentowa': float(okres_data['roznica_procentowa']) * 100,
                        'CzyOkresBazowy': 'TAK' if okres_data['typ_sezonowosci'] == 'Okres bazowy' else 'NIE'
                    }
                    processed_data.append(processed_row)
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Błąd przetwarzania wiersza: {e}")
                continue
        
        return pd.DataFrame(processed_data)
    
    except FileNotFoundError:
        # Jeśli plik nie istnieje, generujemy przykładowe dane
        return generate_sample_data()

def generate_sample_data():
    """Generuje przykładowe dane do testowania aplikacji"""
    sample_data = []
    
    # Definicja kategorii głównych i ich okresów sezonowych
    categories_data = {
        'Komputery': [
            {'okres': 'styczeń - kwiecień', 'typ': 'Okres bazowy', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'maj', 'typ': 'Okazje Wiosenne', 'cena': 1.23, 'roznica': 0.171},
            {'okres': 'czerwiec - wrzesień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.23, 'roznica': 0.171}
        ],
        'Sprzęt AGD': [
            {'okres': 'styczeń - maj', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'czerwiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.44, 'roznica': 0.143},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Sprzęt RTV': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Motoryzacja': [
            {'okres': 'styczeń', 'typ': 'Wyprzedaże Noworoczne', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'luty - czerwiec', 'typ': 'Okres bazowy', 'cena': 1.47, 'roznica': 0.0},
            {'okres': 'lipiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.36, 'roznica': -0.075},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.44, 'roznica': -0.02}
        ],
        'Outlet': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.28, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.49, 'roznica': 0.164}
        ]
    }
    
    for kategoria, okresy in categories_data.items():
        for okres_data in okresy:
            sample_data.append({
                'KategoriaGlowna': kategoria,
                'KategoriaLisc': kategoria,
                'PelnaSciezka': kategoria,
                'MinProwizjaProc': np.random.uniform(1.0, 3.0),
                'MaxProwizjaProc': np.random.uniform(3.0, 8.0),
                'CenaBazowa': np.random.uniform(0.8, 1.8),
                'NazwaOkresu': okres_data['okres'],
                'TypSezonowosci': okres_data['typ'],
                'CenaWOkresie': okres_data['cena'],
                'RoznicaBezwzgledna': okres_data['cena'] - 1.0,
                'RoznicaProcentowa': okres_data['roznica'] * 100,
                'CzyOkresBazowy': 'TAK' if okres_data['typ'] == 'Okres bazowy' else 'NIE'
            })
    
    return pd.DataFrame(sample_data)

# Wczytanie danych
df = load_and_process_data()

# Funkcje pomocnicze do tworzenia dat dla wykresu Gantta
def create_gantt_dates(okres_nazwa):
    """Tworzy daty początkowe i końcowe dla okresów sezonowych"""
    current_year = datetime.now().year
    
    # Mapowanie nazw miesięcy na numery
    months_map = {
        'styczeń': 1, 'luty': 2, 'marzec': 3, 'kwiecień': 4,
        'maj': 5, 'czerwiec': 6, 'lipiec': 7, 'sierpień': 8,
        'wrzesień': 9, 'październik': 10, 'listopad': 11, 'grudzień': 12
    }
    
    try:
        if ' - ' in okres_nazwa:
            start_month, end_month = okres_nazwa.split(' - ')
            start_num = months_map.get(start_month.strip(), 1)
            end_num = months_map.get(end_month.strip(), 12)
        else:
            start_num = end_num = months_map.get(okres_nazwa.strip(), 1)
        
        start_date = datetime(current_year, start_num, 1)
        
        # Obliczenie ostatniego dnia miesiąca końcowego
        if end_num == 12:
            end_date = datetime(current_year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(current_year, end_num + 1, 1) - timedelta(days=1)
        
        return start_date, end_date
    
    except:
        # Domyślne daty w przypadku błędu
        return datetime(current_year, 1, 1), datetime(current_year, 12, 31)

# Stylowanie CSS

# Layout aplikacji
app.layout = html.Div([
    # Nagłówek
    html.Div([
        html.H1("📊 Dashboard Analizy Sezonowości Kategorii",
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'})
    ]),

    # Filtr główny
    html.Div([
        html.Label("🎯 Wybierz kategorię główną:",
                  style={'fontSize': '16px', 'fontWeight': 'bold', 'marginBottom': '10px', 'color': '#2c3e50'}),
        dcc.Dropdown(
            id='kategoria-dropdown',
            options=[{'label': kat, 'value': kat} for kat in sorted(df['KategoriaGlowna'].unique())],
            value=sorted(df['KategoriaGlowna'].unique())[0],
            style={'marginBottom': '30px'}
        )
    ], style={'margin': '20px'}),

    # Drzewko kategorii
    html.Div([
        html.H3("🌳 Struktura Kategorii", style={'color': '#34495e', 'marginBottom': '15px'}),
        dcc.Graph(id='category-tree')
    ], style={'margin': '20px', 'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'}),

    # Sekcja wykresów - uporządkowany layout
    html.Div([
        # Kalendarz sezonowości - pełna szerokość
        html.Div([
            html.H3("📅 Kalendarz Sezonowości", style={'color': '#34495e', 'marginBottom': '15px'}),
            dcc.Graph(id='gantt-chart', style={'height': '400px'})
        ], style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px', 'marginBottom': '20px'}),

        # Wykres słupkowy - pełna szerokość
        html.Div([
            html.H3("📊 Analiza Okresów Sezonowych", style={'color': '#34495e', 'marginBottom': '15px'}),
            dcc.Graph(id='bar-chart', style={'height': '400px'})
        ], style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'})
    ], style={'margin': '20px'}),

    # Kompaktowa tabela
    html.Div([
        html.H3("📋 Podsumowanie Okresów", style={'color': '#34495e', 'marginBottom': '15px'}),
        dash_table.DataTable(
            id='summary-table',
            columns=[
                {'name': 'Okres', 'id': 'Okres'},
                {'name': 'Typ', 'id': 'Typ'},
                {'name': 'Kategorie', 'id': 'LiczbaKategorii'},
                {'name': 'Śr. Cena', 'id': 'SredniaCena', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                {'name': 'Zmiana vs Bazowy', 'id': 'ZmianaProc'},
                {'name': 'Status', 'id': 'Status'}
            ],
            style_cell={'textAlign': 'center', 'padding': '8px', 'fontSize': '13px'},
            style_header={'backgroundColor': '#34495e', 'color': 'white', 'fontWeight': 'bold'},
            style_data_conditional=[
                {
                    'if': {'filter_query': '{Status} = "🔵 Bazowy"'},
                    'backgroundColor': '#e3f2fd'
                },
                {
                    'if': {'filter_query': '{Status} = "🔴 Pik"'},
                    'backgroundColor': '#ffebee'
                },
                {
                    'if': {'filter_query': '{ZmianaProc} contains "+"'},
                    'color': '#2e7d32',
                    'fontWeight': 'bold'
                },
                {
                    'if': {'filter_query': '{ZmianaProc} contains "-"'},
                    'color': '#d32f2f',
                    'fontWeight': 'bold'
                }
            ]
        )
    ], style={'margin': '20px', 'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'})
])

# Callback dla aktualizacji komponentów
@app.callback(
    [Output('category-tree', 'figure'),
     Output('gantt-chart', 'figure'),
     Output('bar-chart', 'figure'),
     Output('summary-table', 'data')],
    [Input('kategoria-dropdown', 'value')]
)
def update_dashboard(selected_category):
    """Aktualizuje wszystkie komponenty dashboardu na podstawie wybranej kategorii"""

    # Filtrowanie danych dla wybranej kategorii
    filtered_df = df[df['KategoriaGlowna'] == selected_category].copy()

    if filtered_df.empty:
        # Zwróć puste komponenty jeśli brak danych
        empty_fig = go.Figure()
        empty_fig.update_layout(title="Brak danych dla wybranej kategorii")
        return empty_fig, empty_fig, empty_fig, []
    
    # Znajdź cenę bazową dla poprawnych obliczeń
    bazowy_okres = filtered_df[filtered_df['CzyOkresBazowy'] == 'TAK']
    cena_bazowa = bazowy_okres['CenaWOkresie'].iloc[0] if not bazowy_okres.empty else 1.0

    # === DRZEWKO KATEGORII ===
    # Grupowanie kategorii i liczenie okresów
    kategorie_stats = []
    for kategoria in filtered_df['KategoriaLisc'].unique():
        kat_data = filtered_df[filtered_df['KategoriaLisc'] == kategoria]
        bazowe = len(kat_data[kat_data['CzyOkresBazowy'] == 'TAK'])
        piki = len(kat_data[kat_data['CzyOkresBazowy'] == 'NIE'])

        kategorie_stats.append({
            'Kategoria': kategoria,
            'Bazowe': bazowe,
            'Piki': piki,
            'Razem': bazowe + piki
        })

    tree_df = pd.DataFrame(kategorie_stats)

    tree_fig = go.Figure()

    # Dodaj słupki dla okresów bazowych
    tree_fig.add_trace(go.Bar(
        name='🔵 Okresy Bazowe',
        x=tree_df['Kategoria'],
        y=tree_df['Bazowe'],
        marker_color='#3498db',
        hovertemplate="<b>%{x}</b><br>Okresy bazowe: %{y}<extra></extra>"
    ))

    # Dodaj słupki dla pików sezonowych
    tree_fig.add_trace(go.Bar(
        name='🔴 Piki Sezonowe',
        x=tree_df['Kategoria'],
        y=tree_df['Piki'],
        marker_color='#e74c3c',
        hovertemplate="<b>%{x}</b><br>Piki sezonowe: %{y}<extra></extra>"
    ))

    tree_fig.update_layout(
        title=f"🌳 Struktura Kategorii: {selected_category}",
        xaxis_title="Kategorie",
        yaxis_title="Liczba Okresów",
        barmode='stack',
        height=300,
        font=dict(size=11)
    )

    # === WYKRES GANTTA ===
    gantt_data = []
    kategorie_count = {}

    for _, row in filtered_df.iterrows():
        start_date, end_date = create_gantt_dates(row['NazwaOkresu'])
        okres_key = row['NazwaOkresu']

        # Liczenie kategorii w okresie
        if okres_key not in kategorie_count:
            kategorie_count[okres_key] = len(filtered_df[filtered_df['NazwaOkresu'] == okres_key]['KategoriaLisc'].unique())

        gantt_data.append({
            'Task': row['NazwaOkresu'],
            'Start': start_date,
            'Finish': end_date,
            'Resource': row['TypSezonowosci'],
            'CzyBazowy': row['CzyOkresBazowy'],
            'Cena': row['CenaWOkresie'],
            'LiczbaKategorii': kategorie_count[okres_key]
        })

    gantt_df = pd.DataFrame(gantt_data).drop_duplicates(subset=['Task'])

    colors = {'TAK': '#3498db', 'NIE': '#e74c3c'}

    gantt_fig = px.timeline(
        gantt_df,
        x_start="Start",
        x_end="Finish",
        y="Task",
        color="CzyBazowy",
        color_discrete_map=colors,
        title=f"📅 Kalendarz Sezonowości: {selected_category}"
    )

    gantt_fig.update_traces(
        hovertemplate="<b>%{y}</b><br>" +
                     "Typ: %{customdata[0]}<br>" +
                     "Kategorie w okresie: %{customdata[3]}<br>" +
                     "Średnia cena: %{customdata[2]:.2f} PLN<br>" +
                     "<extra></extra>"
    )

    gantt_fig.update_layout(
        xaxis_title="Miesiące",
        yaxis_title="Okresy",
        legend_title="Typ Okresu",
        font=dict(size=11),
        showlegend=True
    )

    # Aktualizacja nazw w legendzie
    gantt_fig.for_each_trace(lambda t: t.update(name='🔵 Okres Bazowy' if t.name == 'TAK' else '🔴 Pik Sezonowy'))

    # === WYKRES SŁUPKOWY ===
    # Przygotowanie danych dla wykresu słupkowego
    bar_data = []
    for okres in filtered_df['NazwaOkresu'].unique():
        okres_df = filtered_df[filtered_df['NazwaOkresu'] == okres]
        liczba_kategorii = len(okres_df['KategoriaLisc'].unique())
        srednia_cena = okres_df['CenaWOkresie'].mean()
        typ_sezonowosci = okres_df['TypSezonowosci'].iloc[0]
        czy_bazowy = okres_df['CzyOkresBazowy'].iloc[0]

        # POPRAWIONE OBLICZENIE ZMIANY PROCENTOWEJ
        if czy_bazowy == 'TAK':
            zmiana_proc = 0.0
        else:
            zmiana_proc = ((srednia_cena - cena_bazowa) / cena_bazowa) * 100

        bar_data.append({
            'Okres': okres,
            'LiczbaKategorii': liczba_kategorii,
            'SredniaCena': srednia_cena,
            'TypSezonowosci': typ_sezonowosci,
            'CzyBazowy': czy_bazowy,
            'ZmianaProc': zmiana_proc
        })

    bar_df = pd.DataFrame(bar_data)

    # Wykres słupkowy z liczbą kategorii i zmianą ceny
    bar_fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Liczba Kategorii w Okresach', 'Zmiana Ceny vs Okres Bazowy (%)'),
        vertical_spacing=0.15
    )

    # Górny wykres - liczba kategorii
    colors_bar = ['#3498db' if x == 'TAK' else '#e74c3c' for x in bar_df['CzyBazowy']]

    bar_fig.add_trace(
        go.Bar(
            x=bar_df['Okres'],
            y=bar_df['LiczbaKategorii'],
            marker_color=colors_bar,
            name='Liczba Kategorii',
            hovertemplate="<b>%{x}</b><br>Kategorie: %{y}<br>Typ: %{customdata}<extra></extra>",
            customdata=bar_df['TypSezonowosci']
        ),
        row=1, col=1
    )

    # Dolny wykres - zmiana ceny
    colors_change = ['#2e7d32' if x > 0 else '#d32f2f' if x < 0 else '#757575' for x in bar_df['ZmianaProc']]

    bar_fig.add_trace(
        go.Bar(
            x=bar_df['Okres'],
            y=bar_df['ZmianaProc'],
            marker_color=colors_change,
            name='Zmiana Ceny (%)',
            hovertemplate="<b>%{x}</b><br>Zmiana: %{y:.1f}%<br>Cena: %{customdata:.2f} PLN<extra></extra>",
            customdata=bar_df['SredniaCena']
        ),
        row=2, col=1
    )

    bar_fig.update_layout(
        title=f"📊 Analiza Okresów: {selected_category}",
        height=500,
        showlegend=False,
        font=dict(size=11)
    )

    bar_fig.update_xaxes(title_text="Okresy", row=2, col=1)
    bar_fig.update_yaxes(title_text="Liczba", row=1, col=1)
    bar_fig.update_yaxes(title_text="Zmiana (%)", row=2, col=1)
    
    # === KOMPAKTOWA TABELA PODSUMOWUJĄCA ===
    summary_data = []

    for _, row_data in bar_df.iterrows():
        # Formatowanie zmiany procentowej
        if row_data['ZmianaProc'] == 0:
            zmiana_str = "0.0% (bazowy)"
        elif row_data['ZmianaProc'] > 0:
            zmiana_str = f"+{row_data['ZmianaProc']:.1f}%"
        else:
            zmiana_str = f"{row_data['ZmianaProc']:.1f}%"

        # Status okresu
        status = "🔵 Bazowy" if row_data['CzyBazowy'] == 'TAK' else "🔴 Pik"

        summary_data.append({
            'Okres': row_data['Okres'],
            'Typ': row_data['TypSezonowosci'],
            'LiczbaKategorii': row_data['LiczbaKategorii'],
            'SredniaCena': row_data['SredniaCena'],
            'ZmianaProc': zmiana_str,
            'Status': status
        })

    return tree_fig, gantt_fig, bar_fig, summary_data

# Uruchomienie aplikacji
if __name__ == '__main__':
    print("Uruchamianie Dashboard Analizy Sezonowości Kategorii...")
    print("Aplikacja będzie dostępna pod adresem: http://127.0.0.1:8050/")
    app.run(debug=True, host='127.0.0.1', port=8050)
