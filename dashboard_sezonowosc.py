#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard Analizy Sezonowości Kategorii
Interaktywny dashboard do analizy sezonowości kategorii produktów
z wykorzystaniem danych CPC i prowizji "Kup Teraz"
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, dash_table, Input, Output, callback
import json
from datetime import datetime, timedelta
import numpy as np

# Konfiguracja aplikacji Dash
external_stylesheets = ['https://codepen.io/chriddyp/pen/bWLwgP.css']
app = dash.Dash(__name__, external_stylesheets=external_stylesheets)
app.title = "Dashboard Analizy Sezonowości Kategorii"

# Funkcja do wczytania i przetworzenia danych
def load_and_process_data():
    """Wczytuje i przetwarza dane z pliku CSV"""
    try:
        # Wczytanie danych z pliku CSV
        df = pd.read_csv('report/CENEO_CPC_COMMISION_MODEL.csv')
        
        # Lista do przechowywania przetworzonych danych
        processed_data = []
        
        for _, row in df.iterrows():
            try:
                # Parsowanie JSON z analizy cen
                analiza_json = json.loads(row['analiza_cen_w_okresach'])
                
                for okres_data in analiza_json['analiza_cen_w_okresach']:
                    processed_row = {
                        'KategoriaGlowna': row['kategoria_glowna'],
                        'KategoriaLisc': row['leaf_category'],
                        'PelnaSciezka': row['pelna_sciezka'],
                        'MinProwizjaProc': row['min_commision_perc'] if pd.notna(row['min_commision_perc']) else 0,
                        'MaxProwizjaProc': row['max_commision_perc'] if pd.notna(row['max_commision_perc']) else 0,
                        'CenaBazowa': float(row['cena_bazowa_kategorii_pln']),
                        'NazwaOkresu': okres_data['okres'],
                        'TypSezonowosci': okres_data['typ_sezonowosci'],
                        'CenaWOkresie': float(okres_data['cena_w_okresie']),
                        'RoznicaBezwzgledna': float(okres_data['roznica_bezwzgledna_pln']),
                        'RoznicaProcentowa': float(okres_data['roznica_procentowa']) * 100,
                        'CzyOkresBazowy': 'TAK' if okres_data['typ_sezonowosci'] == 'Okres bazowy' else 'NIE'
                    }
                    processed_data.append(processed_row)
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Błąd przetwarzania wiersza: {e}")
                continue
        
        return pd.DataFrame(processed_data)
    
    except FileNotFoundError:
        # Jeśli plik nie istnieje, generujemy przykładowe dane
        return generate_sample_data()

def generate_sample_data():
    """Generuje przykładowe dane do testowania aplikacji"""
    sample_data = []
    
    # Definicja kategorii głównych i ich okresów sezonowych
    categories_data = {
        'Komputery': [
            {'okres': 'styczeń - kwiecień', 'typ': 'Okres bazowy', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'maj', 'typ': 'Okazje Wiosenne', 'cena': 1.23, 'roznica': 0.171},
            {'okres': 'czerwiec - wrzesień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.05, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.23, 'roznica': 0.171}
        ],
        'Sprzęt AGD': [
            {'okres': 'styczeń - maj', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'czerwiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.44, 'roznica': 0.143},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Sprzęt RTV': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.26, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.59, 'roznica': 0.262}
        ],
        'Motoryzacja': [
            {'okres': 'styczeń', 'typ': 'Wyprzedaże Noworoczne', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'luty - czerwiec', 'typ': 'Okres bazowy', 'cena': 1.47, 'roznica': 0.0},
            {'okres': 'lipiec - sierpień', 'typ': 'Wakacyjna (Lato)', 'cena': 1.22, 'roznica': -0.17},
            {'okres': 'wrzesień', 'typ': 'Jesienna', 'cena': 1.36, 'roznica': -0.075},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.44, 'roznica': -0.02}
        ],
        'Outlet': [
            {'okres': 'styczeń - wrzesień', 'typ': 'Okres bazowy', 'cena': 1.28, 'roznica': 0.0},
            {'okres': 'październik - grudzień', 'typ': 'Black Friday', 'cena': 1.49, 'roznica': 0.164}
        ]
    }
    
    for kategoria, okresy in categories_data.items():
        for okres_data in okresy:
            sample_data.append({
                'KategoriaGlowna': kategoria,
                'KategoriaLisc': kategoria,
                'PelnaSciezka': kategoria,
                'MinProwizjaProc': np.random.uniform(1.0, 3.0),
                'MaxProwizjaProc': np.random.uniform(3.0, 8.0),
                'CenaBazowa': np.random.uniform(0.8, 1.8),
                'NazwaOkresu': okres_data['okres'],
                'TypSezonowosci': okres_data['typ'],
                'CenaWOkresie': okres_data['cena'],
                'RoznicaBezwzgledna': okres_data['cena'] - 1.0,
                'RoznicaProcentowa': okres_data['roznica'] * 100,
                'CzyOkresBazowy': 'TAK' if okres_data['typ'] == 'Okres bazowy' else 'NIE'
            })
    
    return pd.DataFrame(sample_data)

# Wczytanie danych
df = load_and_process_data()

# Funkcje pomocnicze do tworzenia dat dla wykresu Gantta
def create_gantt_dates(okres_nazwa):
    """Tworzy daty początkowe i końcowe dla okresów sezonowych"""
    current_year = datetime.now().year
    
    # Mapowanie nazw miesięcy na numery
    months_map = {
        'styczeń': 1, 'luty': 2, 'marzec': 3, 'kwiecień': 4,
        'maj': 5, 'czerwiec': 6, 'lipiec': 7, 'sierpień': 8,
        'wrzesień': 9, 'październik': 10, 'listopad': 11, 'grudzień': 12
    }
    
    try:
        if ' - ' in okres_nazwa:
            start_month, end_month = okres_nazwa.split(' - ')
            start_num = months_map.get(start_month.strip(), 1)
            end_num = months_map.get(end_month.strip(), 12)
        else:
            start_num = end_num = months_map.get(okres_nazwa.strip(), 1)
        
        start_date = datetime(current_year, start_num, 1)
        
        # Obliczenie ostatniego dnia miesiąca końcowego
        if end_num == 12:
            end_date = datetime(current_year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(current_year, end_num + 1, 1) - timedelta(days=1)
        
        return start_date, end_date
    
    except:
        # Domyślne daty w przypadku błędu
        return datetime(current_year, 1, 1), datetime(current_year, 12, 31)

# Stylowanie CSS

# Layout aplikacji
app.layout = html.Div([
    # Nagłówek z opisem
    html.Div([
        html.H1("📊 Dashboard Analizy Sezonowości Kategorii",
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '10px'}),
        html.P("Analiza okresów sezonowych dla optymalizacji stawek CPC i strategii cenowych",
               style={'textAlign': 'center', 'color': '#7f8c8d', 'fontSize': '18px', 'marginBottom': '30px'})
    ]),

    # Filtr główny z opisem
    html.Div([
        html.Div([
            html.Label("🎯 Wybierz kategorię główną do analizy:",
                      style={'fontSize': '16px', 'fontWeight': 'bold', 'marginBottom': '10px', 'color': '#2c3e50'}),
            dcc.Dropdown(
                id='kategoria-dropdown',
                options=[{'label': kat, 'value': kat} for kat in sorted(df['KategoriaGlowna'].unique())],
                value=sorted(df['KategoriaGlowna'].unique())[0],
                style={'marginBottom': '20px'}
            )
        ], className='six columns'),

        # Legenda dla wykresów
        html.Div([
            html.H4("📖 Legenda", style={'color': '#2c3e50', 'marginBottom': '15px'}),
            html.Div([
                html.Span("🔵 Okres Bazowy", style={'color': '#3498db', 'fontWeight': 'bold', 'marginRight': '20px'}),
                html.Span("- standardowy poziom ruchu i cen", style={'color': '#7f8c8d'})
            ], style={'marginBottom': '8px'}),
            html.Div([
                html.Span("🔴 Pik Sezonowy", style={'color': '#e74c3c', 'fontWeight': 'bold', 'marginRight': '20px'}),
                html.Span("- wzmożone zainteresowanie, potencjał na wyższe stawki", style={'color': '#7f8c8d'})
            ])
        ], className='six columns', style={'backgroundColor': '#ecf0f1', 'padding': '15px', 'borderRadius': '8px'})
    ], className='row', style={'margin': '20px'}),

    # Sekcja z wykresami - dwie kolumny
    html.Div([
        # Lewa kolumna - Wykres Gantta
        html.Div([
            html.H3("📅 Kalendarz Sezonowości",
                    style={'color': '#34495e', 'marginBottom': '15px'}),
            html.P("Wizualizacja okresów sezonowych w ciągu roku. Każdy pasek reprezentuje okres o określonej charakterystyce cenowej.",
                   style={'color': '#7f8c8d', 'fontSize': '14px', 'marginBottom': '15px'}),
            dcc.Graph(id='gantt-chart')
        ], className='seven columns', style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px', 'marginRight': '10px'}),

        # Prawa kolumna - Wykres kołowy
        html.Div([
            html.H3("🥧 Rozkład Okresów",
                    style={'color': '#34495e', 'marginBottom': '15px'}),
            html.P("Proporcje różnych typów okresów sezonowych w wybranej kategorii.",
                   style={'color': '#7f8c8d', 'fontSize': '14px', 'marginBottom': '15px'}),
            dcc.Graph(id='pie-chart')
        ], className='five columns', style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px'})
    ], className='row', style={'margin': '20px'}),

    # Tabela hierarchiczna z kategoriami i okresami
    html.Div([
        html.H3("📋 Szczegółowa Analiza Okresów",
                style={'color': '#34495e', 'marginBottom': '15px'}),
        html.P("Hierarchiczna tabela pokazująca wszystkie podkategorie i ich okresy sezonowe z analizą cenową.",
               style={'color': '#7f8c8d', 'fontSize': '14px', 'marginBottom': '20px'}),

        # Objaśnienia kolumn
        html.Div([
            html.H5("💡 Objaśnienie kolumn:", style={'color': '#2c3e50', 'marginBottom': '10px'}),
            html.Ul([
                html.Li("📂 Kategoria/Okres - hierarchia kategorii i ich okresów sezonowych"),
                html.Li("🏷️ Typ Sezonowości - rodzaj okresu (bazowy, promocyjny, sezonowy)"),
                html.Li("💰 Prowizja - zakres prowizji min-max dla kategorii"),
                html.Li("💵 Cena - średnia cena w danym okresie (PLN)"),
                html.Li("📈 Zmiana vs Bazowy - procentowa zmiana ceny względem okresu bazowego"),
                html.Li("🎯 Status - czy to okres bazowy czy pik sezonowy")
            ], style={'color': '#7f8c8d', 'fontSize': '13px'})
        ], style={'backgroundColor': '#ecf0f1', 'padding': '15px', 'borderRadius': '8px', 'marginBottom': '20px'}),

        dash_table.DataTable(
            id='hierarchical-table',
            columns=[
                {'name': '📂 Kategoria/Okres', 'id': 'Nazwa', 'presentation': 'markdown'},
                {'name': '🏷️ Typ Sezonowości', 'id': 'TypSezonowosci'},
                {'name': '💰 Prowizja (%)', 'id': 'ZakresProwizji'},
                {'name': '💵 Cena (PLN)', 'id': 'CenaWOkresie', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                {'name': '📈 Zmiana vs Bazowy', 'id': 'CenaVsBazowy'},
                {'name': '🎯 Status', 'id': 'StatusOkresu'}
            ],
            style_cell={
                'textAlign': 'left',
                'padding': '12px',
                'fontFamily': 'Arial, sans-serif',
                'fontSize': '14px'
            },
            style_header={
                'backgroundColor': '#34495e',
                'color': 'white',
                'fontWeight': 'bold',
                'textAlign': 'center'
            },
            style_data_conditional=[
                # Kategorie główne - pogrubione
                {
                    'if': {'filter_query': '{Typ} = kategoria'},
                    'backgroundColor': '#3498db',
                    'color': 'white',
                    'fontWeight': 'bold'
                },
                # Okresy bazowe
                {
                    'if': {'filter_query': '{StatusOkresu} = "🔵 Okres Bazowy"'},
                    'backgroundColor': '#ebf3fd',
                    'border': '1px solid #3498db'
                },
                # Piki sezonowe
                {
                    'if': {'filter_query': '{StatusOkresu} = "🔴 Pik Sezonowy"'},
                    'backgroundColor': '#fdf2f2',
                    'border': '1px solid #e74c3c'
                },
                # Pozytywne zmiany cen
                {
                    'if': {'filter_query': '{CenaVsBazowy} contains "+"'},
                    'color': '#27ae60',
                    'fontWeight': 'bold'
                },
                # Negatywne zmiany cen
                {
                    'if': {'filter_query': '{CenaVsBazowy} contains "-"'},
                    'color': '#e74c3c',
                    'fontWeight': 'bold'
                }
            ],
            style_table={'overflowX': 'auto'},
            page_size=20
        )
    ], style={'margin': '20px', 'backgroundColor': '#f8f9fa', 'padding': '25px', 'borderRadius': '10px'})
])

# Callback dla aktualizacji komponentów
@app.callback(
    [Output('gantt-chart', 'figure'),
     Output('pie-chart', 'figure'),
     Output('hierarchical-table', 'data')],
    [Input('kategoria-dropdown', 'value')]
)
def update_dashboard(selected_category):
    """Aktualizuje wszystkie komponenty dashboardu na podstawie wybranej kategorii"""

    # Filtrowanie danych dla wybranej kategorii
    filtered_df = df[df['KategoriaGlowna'] == selected_category].copy()

    if filtered_df.empty:
        # Zwróć puste komponenty jeśli brak danych
        empty_fig = go.Figure()
        empty_fig.update_layout(title="Brak danych dla wybranej kategorii")
        return empty_fig, empty_fig, []
    
    # === WYKRES GANTTA ===
    gantt_data = []
    for _, row in filtered_df.iterrows():
        start_date, end_date = create_gantt_dates(row['NazwaOkresu'])

        gantt_data.append({
            'Task': row['NazwaOkresu'],
            'Start': start_date,
            'Finish': end_date,
            'Resource': row['TypSezonowosci'],
            'CzyBazowy': row['CzyOkresBazowy'],
            'Cena': row['CenaWOkresie']
        })

    gantt_df = pd.DataFrame(gantt_data)

    # Lepsze kolory i etykiety
    colors = {'TAK': '#3498db', 'NIE': '#e74c3c'}
    labels = {'TAK': '🔵 Okres Bazowy', 'NIE': '🔴 Pik Sezonowy'}

    gantt_fig = px.timeline(
        gantt_df,
        x_start="Start",
        x_end="Finish",
        y="Task",
        color="CzyBazowy",
        color_discrete_map=colors,
        title=f"📅 Kalendarz Sezonowości: {selected_category}",
        hover_data={"Resource": True, "Cena": ":.2f"}
    )

    # Aktualizacja etykiet legendy
    gantt_fig.for_each_trace(lambda t: t.update(name=labels.get(t.name, t.name)))

    gantt_fig.update_layout(
        height=400,
        xaxis_title="📅 Miesiące",
        yaxis_title="🏷️ Okresy Sezonowe",
        legend_title="🎯 Typ Okresu",
        font=dict(size=12),
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    gantt_fig.update_traces(
        hovertemplate="<b>%{y}</b><br>" +
                     "Okres: %{base} - %{x}<br>" +
                     "Typ: %{customdata[0]}<br>" +
                     "Cena: %{customdata[1]:.2f} PLN<br>" +
                     "<extra></extra>"
    )

    # === WYKRES KOŁOWY ===
    pie_data = filtered_df.groupby('TypSezonowosci').size().reset_index(name='Liczba')

    # Mapowanie kolorów dla wykresu kołowego
    color_map = {
        'Okres bazowy': '#3498db',
        'Black Friday': '#e74c3c',
        'Wakacyjna (Lato)': '#f39c12',
        'Okazje Wiosenne': '#2ecc71',
        'Wyprzedaże Noworoczne': '#9b59b6',
        'Jesienna': '#d35400',
        'Powrót do szkoły': '#1abc9c'
    }

    pie_fig = px.pie(
        pie_data,
        values='Liczba',
        names='TypSezonowosci',
        title=f"🥧 Rozkład Typów Sezonowości",
        color='TypSezonowosci',
        color_discrete_map=color_map
    )

    pie_fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate="<b>%{label}</b><br>" +
                     "Liczba okresów: %{value}<br>" +
                     "Udział: %{percent}<br>" +
                     "<extra></extra>"
    )

    pie_fig.update_layout(
        height=400,
        font=dict(size=11),
        showlegend=True,
        legend=dict(orientation="v", yanchor="middle", y=0.5, xanchor="left", x=1.05)
    )
    
    # === HIERARCHICZNA TABELA ===
    table_data = []

    # Znajdź cenę bazową (okres bazowy)
    bazowy_okres = filtered_df[filtered_df['CzyOkresBazowy'] == 'TAK']
    cena_bazowa = bazowy_okres['CenaWOkresie'].iloc[0] if not bazowy_okres.empty else 1.0

    # Grupowanie po kategoriach liść
    kategorie_lisc = filtered_df['KategoriaLisc'].unique()

    for kategoria_lisc in sorted(kategorie_lisc):
        # Dodaj wiersz kategorii głównej
        kat_data = filtered_df[filtered_df['KategoriaLisc'] == kategoria_lisc].iloc[0]

        # Oblicz średnią prowizję dla kategorii
        min_prow = kat_data['MinProwizjaProc']
        max_prow = kat_data['MaxProwizjaProc']

        if min_prow == max_prow and min_prow > 0:
            zakres_prowizji = f"{min_prow:.1f}%"
        elif min_prow > 0 or max_prow > 0:
            zakres_prowizji = f"{min_prow:.1f}% - {max_prow:.1f}%"
        else:
            zakres_prowizji = "Brak danych"

        # Wiersz kategorii
        table_data.append({
            'Nazwa': f"**📂 {kategoria_lisc}**",
            'TypSezonowosci': "KATEGORIA",
            'ZakresProwizji': zakres_prowizji,
            'CenaWOkresie': "",
            'CenaVsBazowy': "",
            'StatusOkresu': "📁 Kategoria",
            'Typ': 'kategoria'
        })

        # Dodaj okresy dla tej kategorii
        okresy_kat = filtered_df[filtered_df['KategoriaLisc'] == kategoria_lisc]

        for _, row in okresy_kat.iterrows():
            # Obliczenie różnicy względem okresu bazowego
            if row['CzyOkresBazowy'] == 'TAK':
                cena_vs_bazowy = "0.0% (bazowy)"
                status_okresu = "🔵 Okres Bazowy"
            else:
                roznica_proc = ((row['CenaWOkresie'] - cena_bazowa) / cena_bazowa) * 100
                if roznica_proc > 0:
                    cena_vs_bazowy = f"+{roznica_proc:.1f}%"
                else:
                    cena_vs_bazowy = f"{roznica_proc:.1f}%"
                status_okresu = "🔴 Pik Sezonowy"

            # Wiersz okresu (z wcięciem)
            table_data.append({
                'Nazwa': f"    ⏰ {row['NazwaOkresu']}",
                'TypSezonowosci': row['TypSezonowosci'],
                'ZakresProwizji': "-",
                'CenaWOkresie': row['CenaWOkresie'],
                'CenaVsBazowy': cena_vs_bazowy,
                'StatusOkresu': status_okresu,
                'Typ': 'okres'
            })

    return gantt_fig, pie_fig, table_data

# Uruchomienie aplikacji
if __name__ == '__main__':
    print("Uruchamianie Dashboard Analizy Sezonowości Kategorii...")
    print("Aplikacja będzie dostępna pod adresem: http://127.0.0.1:8050/")
    app.run(debug=True, host='127.0.0.1', port=8050)
