#!/usr/bin/env python3
"""
Debug script do sprawdzenia różnic między węzłami z danymi cenowymi i bez.
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def navigate_to_page(driver):
    driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
    logger.info("Nawigacja do strony zakończona")
    
    try:
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty")
    
    time.sleep(5)
    logger.info("Strona załadowana")

def get_category_name(node):
    """Wyciąga nazwę kategorii z węzła."""
    try:
        text = node.text.strip()
        if not text:
            return None
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        for line in lines:
            if line not in ['chevron_right', 'expand_more'] and 'PLN' not in line:
                return line
        
        return None
    except:
        return None

def get_pricing_info(node):
    """Wyciąga informacje o cenach z węzła."""
    try:
        text = node.text.strip()
        if not text:
            return []
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        pricing_data = []
        
        for line in lines:
            if line in ['chevron_right', 'expand_more']:
                continue
            
            if 'PLN' in line and ':' in line:
                parts = line.split(':')
                if len(parts) == 2:
                    period = parts[0].strip()
                    price_part = parts[1].strip().replace('PLN', '').strip()
                    
                    if ' - ' in price_part:
                        prices = price_part.split(' - ')
                        if len(prices) == 2:
                            min_price = prices[0].strip()
                            max_price = prices[1].strip()
                            pricing_data.append({
                                'period': period,
                                'min_price': min_price,
                                'max_price': max_price
                            })
        
        return pricing_data
    except Exception as e:
        logger.warning(f"Błąd parsowania cen: {e}")
        return []

def analyze_node_detailed(driver, node, node_name):
    """Szczegółowa analiza węzła."""
    logger.info(f"\n=== ANALIZA: {node_name} ===")
    
    # Podstawowe informacje
    level = node.get_attribute('aria-level')
    data_cy = node.get_attribute('data-cy')
    logger.info(f"Poziom: {level}, data-cy: {data_cy}")
    
    # Pełny tekst
    full_text = node.text.strip()
    logger.info(f"Pełny tekst:\n{repr(full_text)}")
    
    # Linie tekstu
    lines = [line.strip() for line in full_text.split('\n') if line.strip()]
    logger.info(f"Linie ({len(lines)}):")
    for i, line in enumerate(lines):
        has_pln = 'PLN' in line
        has_colon = ':' in line
        logger.info(f"  {i}: {repr(line)} [PLN: {has_pln}, ':': {has_colon}]")
    
    # Sprawdź elementy cenowe
    chip_elements = node.find_elements(By.CSS_SELECTOR, "mat-chip-listbox, mat-chip")
    logger.info(f"Elementy chip: {len(chip_elements)}")
    for i, chip in enumerate(chip_elements):
        try:
            chip_text = chip.text.strip()
            logger.info(f"  CHIP {i}: {repr(chip_text)}")
        except:
            pass
    
    # Sprawdź dane cenowe
    pricing_data = get_pricing_info(node)
    logger.info(f"Dane cenowe: {len(pricing_data)} rekordów")
    for price in pricing_data:
        logger.info(f"  - {price['period']}: {price['min_price']} - {price['max_price']} PLN")
    
    return len(pricing_data) > 0

def compare_nodes(driver):
    """Porównuje węzły z danymi cenowymi i bez."""
    
    # Rozwiń pierwszą kategorię
    all_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
    first_node = all_nodes[0]
    
    expand_button = first_node.find_element(By.CSS_SELECTOR, "mat-icon")
    if expand_button.text == 'chevron_right':
        logger.info("Rozwijam pierwszą kategorię...")
        driver.execute_script("arguments[0].click();", expand_button)
        time.sleep(5)
    
    # Pobierz wszystkie węzły po rozwinięciu
    all_nodes_after = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
    logger.info(f"Węzłów po rozwinięciu: {len(all_nodes_after)}")
    
    nodes_with_pricing = []
    nodes_without_pricing = []
    
    # Klasyfikuj węzły
    for node in all_nodes_after:
        try:
            category_name = get_category_name(node)
            if not category_name:
                continue
                
            pricing_data = get_pricing_info(node)
            
            if pricing_data:
                nodes_with_pricing.append((node, category_name))
            else:
                nodes_without_pricing.append((node, category_name))
        except:
            continue
    
    logger.info(f"\nWęzły Z cenami: {len(nodes_with_pricing)}")
    logger.info(f"Węzły BEZ cen: {len(nodes_without_pricing)}")
    
    # Analizuj węzły Z cenami
    logger.info(f"\n{'='*50}")
    logger.info("WĘZŁY Z CENAMI")
    logger.info(f"{'='*50}")
    
    for i, (node, name) in enumerate(nodes_with_pricing[:2]):
        analyze_node_detailed(driver, node, f"Z_CENAMI_{name}")
    
    # Analizuj węzły BEZ cen
    logger.info(f"\n{'='*50}")
    logger.info("WĘZŁY BEZ CEN")
    logger.info(f"{'='*50}")
    
    for i, (node, name) in enumerate(nodes_without_pricing[:3]):
        analyze_node_detailed(driver, node, f"BEZ_CEN_{name}")
        
        # Poczekaj i sprawdź ponownie
        logger.info(f"Czekam 3 sekundy...")
        time.sleep(3)
        
        has_pricing_after = analyze_node_detailed(driver, node, f"BEZ_CEN_{name}_PO_CZEKANIU")
        if has_pricing_after:
            logger.info("SUKCES! Pojawiły się ceny!")
        else:
            logger.info("NADAL BRAK CEN")

def main():
    driver = setup_driver()
    
    try:
        navigate_to_page(driver)
        compare_nodes(driver)
        
    except Exception as e:
        logger.error(f"Błąd: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
