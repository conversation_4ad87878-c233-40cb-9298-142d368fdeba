#!/usr/bin/env python3
"""
Debug script do sprawdzenia jak wyglądają dane cenowe w różnych kategoriach.
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def navigate_to_page(driver):
    driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
    logger.info("Nawigacja do strony zakończona")
    
    try:
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty")
    
    time.sleep(5)
    logger.info("Strona załadowana")

def analyze_node_content(driver, node, node_index):
    """Analizuje zawartość węzła w szczegółach."""
    try:
        logger.info(f"\n=== ANALIZA WĘZŁA {node_index} ===")
        
        # Podstawowe informacje
        level = node.get_attribute('aria-level')
        data_cy = node.get_attribute('data-cy')
        logger.info(f"Poziom: {level}, data-cy: {data_cy}")
        
        # Cały tekst węzła
        full_text = node.text.strip()
        logger.info(f"Pełny tekst węzła:\n{repr(full_text)}")
        
        # Podziel na linie
        lines = [line.strip() for line in full_text.split('\n') if line.strip()]
        logger.info(f"Linie tekstu ({len(lines)}):")
        for i, line in enumerate(lines):
            logger.info(f"  {i}: {repr(line)}")
        
        # Sprawdź wszystkie elementy wewnętrzne
        all_elements = node.find_elements(By.CSS_SELECTOR, "*")
        logger.info(f"Elementy wewnętrzne ({len(all_elements)}):")
        for i, elem in enumerate(all_elements[:10]):  # Tylko pierwsze 10
            try:
                tag = elem.tag_name
                text = elem.text.strip()
                classes = elem.get_attribute('class')
                logger.info(f"  {i}: <{tag}> class='{classes}' text='{text[:50]}...'")
            except:
                pass
        
        # Sprawdź czy są elementy z cenami
        price_elements = node.find_elements(By.CSS_SELECTOR, "*")
        price_texts = []
        for elem in price_elements:
            try:
                text = elem.text.strip()
                if 'PLN' in text or ':' in text:
                    price_texts.append(text)
            except:
                pass
        
        if price_texts:
            logger.info(f"Znalezione teksty z cenami:")
            for price_text in price_texts:
                logger.info(f"  - {repr(price_text)}")
        else:
            logger.info("Brak tekstów z cenami")
        
        return True
        
    except Exception as e:
        logger.error(f"Błąd analizy węzła: {e}")
        return False

def main():
    driver = setup_driver()
    
    try:
        navigate_to_page(driver)
        
        # Znajdź wszystkie węzły
        all_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
        logger.info(f"Znaleziono {len(all_nodes)} węzłów")
        
        # Analizuj pierwsze 5 węzłów
        for i in range(min(5, len(all_nodes))):
            analyze_node_content(driver, all_nodes[i], i)
            time.sleep(1)
        
        # Rozwiń pierwszą kategorię i sprawdź ponownie
        logger.info("\n=== ROZWIJANIE PIERWSZEJ KATEGORII ===")
        first_node = all_nodes[0]
        expand_button = first_node.find_element(By.CSS_SELECTOR, "mat-icon")
        if expand_button.text == 'chevron_right':
            driver.execute_script("arguments[0].click();", expand_button)
            logger.info("Rozwinięto pierwszą kategorię")
            time.sleep(3)
            
            # Sprawdź ponownie węzły
            all_nodes_after = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            logger.info(f"Po rozwinięciu: {len(all_nodes_after)} węzłów")
            
            # Analizuj nowe węzły (podkategorie)
            for i in range(len(all_nodes), min(len(all_nodes) + 3, len(all_nodes_after))):
                analyze_node_content(driver, all_nodes_after[i], i)
                time.sleep(1)
        
    except Exception as e:
        logger.error(f"Błąd: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
