#!/bin/bash

# Skrypt uruchamiający Dashboard Analizy <PERSON>owości Kategorii

echo "🚀 Uruchamianie Dashboard Analizy Sezonowości Kategorii..."
echo ""

# Sprawdzenie czy Python jest zainstalowany
if ! command -v python &> /dev/null; then
    echo "❌ Python nie jest zainstalowany. Zainstaluj Python 3.7+ i spróbuj ponownie."
    exit 1
fi

# Sprawdzenie czy wymagane biblioteki są zainstalowane
echo "📦 Sprawdzanie zależności..."
python -c "import pandas, plotly, dash, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📥 Instalowanie wymaganych bibliotek..."
    pip install plotly dash numpy pandas
    if [ $? -ne 0 ]; then
        echo "❌ Błąd podczas instalacji bibliotek. Sprawdź połączenie internetowe."
        exit 1
    fi
fi

echo "✅ Wszystkie zależności są zainstalowane."
echo ""

# Sprawdzenie czy plik dashboard istnieje
if [ ! -f "dashboard_sezonowosc.py" ]; then
    echo "❌ Plik dashboard_sezonowosc.py nie został znaleziony w bieżącym katalogu."
    exit 1
fi

echo "🌐 Uruchamianie aplikacji..."
echo "📍 Dashboard będzie dostępny pod adresem: http://127.0.0.1:8050/"
echo ""
echo "💡 Aby zatrzymać aplikację, naciśnij Ctrl+C"
echo ""

# Uruchomienie aplikacji
python dashboard_sezonowosc.py
