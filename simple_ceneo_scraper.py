#!/usr/bin/env python3
"""
Prosty scraper Ceneo CPC - rozwija kategorie i zapisuje dane bezpośrednio do CSV.
"""

import logging
import csv
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleCeneoScraper:
    def __init__(self):
        self.driver = None
        self.csv_file = 'ceneo_cpc_data.csv'
        self.processed_categories = set()
        
    def setup_driver(self):
        """Konfiguracja przeglądarki Chrome."""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Przeglądarka uruchomiona")

    def navigate_to_page(self):
        """Nawigacja do strony Ceneo."""
        self.driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
        logger.info("Nawigacja do strony zakończona")
        
        # Czeka na załadowanie
        try:
            WebDriverWait(self.driver, 30).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
            )
            logger.info("Splash screen zniknął")
        except:
            logger.info("Splash screen nie został wykryty")
        
        time.sleep(5)
        logger.info("Strona załadowana")

    def get_category_name(self, node):
        """Wyciąga nazwę kategorii z węzła."""
        try:
            text = node.text.strip()
            if not text:
                return None
            
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            # Znajdź pierwszą linię która nie jest ikoną ani ceną
            for line in lines:
                if line not in ['chevron_right', 'expand_more'] and 'PLN' not in line:
                    return line
            
            return None
        except:
            return None

    def get_pricing_info(self, node):
        """Wyciąga informacje o cenach z węzła."""
        try:
            text = node.text.strip()
            if not text:
                return []

            lines = [line.strip() for line in text.split('\n') if line.strip()]
            pricing_data = []

            for line in lines:
                # Pomiń ikony i nazwy kategorii
                if line in ['chevron_right', 'expand_more']:
                    continue

                # Szukaj linii z cenami (zawierają PLN i :)
                if 'PLN' in line and ':' in line:
                    # Format: "styczeń - marzec: 0,52 - 1,22 PLN" lub "styczeń - wrzesień: 0,82 PLN"
                    parts = line.split(':')
                    if len(parts) == 2:
                        period = parts[0].strip()
                        price_part = parts[1].strip().replace('PLN', '').strip()

                        if ' - ' in price_part:
                            # Zakres cen: min - max
                            prices = price_part.split(' - ')
                            if len(prices) == 2:
                                min_price = prices[0].strip()
                                max_price = prices[1].strip()
                                pricing_data.append({
                                    'period': period,
                                    'min_price': min_price,
                                    'max_price': max_price
                                })
                        else:
                            # Pojedyncza cena
                            single_price = price_part.strip()
                            if single_price:
                                pricing_data.append({
                                    'period': period,
                                    'min_price': single_price,
                                    'max_price': single_price
                                })

            return pricing_data
        except Exception as e:
            logger.warning(f"Błąd parsowania cen: {e}")
            return []

    def has_expand_button(self, node):
        """Sprawdza czy węzeł ma przycisk rozwijania."""
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return True
            return False
        except:
            return False

    def get_expand_button(self, node):
        """Zwraca przycisk rozwijania dla węzła."""
        try:
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text == 'chevron_right':
                    return icon
            return None
        except:
            return None

    def count_children(self, parent_level):
        """Liczy dzieci na następnym poziomie."""
        try:
            next_level = parent_level + 1
            children = self.driver.find_elements(By.CSS_SELECTOR, f"mat-tree-node[aria-level='{next_level}']")
            return len(children)
        except:
            return 0

    def build_category_path(self, current_node, level):
        """Buduje pełną ścieżkę kategorii."""
        try:
            path_parts = []
            current_name = self.get_category_name(current_node)
            
            if level == 1:
                return current_name if current_name else "Nieznana kategoria"
            
            # Dla wyższych poziomów, znajdź rodzica
            # Uproszczenie - użyj pozycji w DOM
            all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
            current_index = all_nodes.index(current_node)
            
            # Znajdź najbliższy węzeł z niższym poziomem przed tym węzłem
            for i in range(current_index - 1, -1, -1):
                node = all_nodes[i]
                node_level = int(node.get_attribute('aria-level'))
                if node_level == level - 1:
                    parent_name = self.get_category_name(node)
                    if parent_name:
                        parent_path = self.build_category_path(node, node_level)
                        return f"{parent_path} > {current_name}" if current_name else parent_path
                    break
            
            return current_name if current_name else "Nieznana kategoria"
        except:
            return "Błąd ścieżki"

    def process_and_save_categories(self):
        """Przetwarza kategorie i zapisuje do CSV."""
        logger.info("Rozpoczęcie przetwarzania kategorii...")
        
        with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
            fieldnames = [
                'pelna_sciezka_kategorii', 'kategoria_glowna', 'parent_kategorii',
                'nazwa_kategorii_ostatecznej', 'czy_kategoria_ostateczna', 'liczba_podkategorii',
                'okres_czasowy', 'cena_minimalna_pln', 'cena_maksymalna_pln'
            ]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            
            record_count = 0
            iteration = 0
            
            while True:
                iteration += 1
                logger.info(f"=== ITERACJA {iteration} ===")
                
                # Znajdź wszystkie węzły
                all_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                logger.info(f"Znaleziono {len(all_nodes)} węzłów")
                
                # Znajdź węzły do rozwinięcia
                nodes_to_expand = []
                for node in all_nodes:
                    if self.has_expand_button(node):
                        node_id = node.get_attribute('data-cy')
                        if node_id not in self.processed_categories:
                            nodes_to_expand.append(node)
                
                logger.info(f"Węzłów do rozwinięcia: {len(nodes_to_expand)}")
                
                # Jeśli nie ma węzłów do rozwinięcia, przetwórz wszystkie i zakończ
                if not nodes_to_expand:
                    logger.info("Brak węzłów do rozwinięcia - przetwarzanie wszystkich kategorii")
                    
                    for node in all_nodes:
                        try:
                            node_id = node.get_attribute('data-cy')
                            if node_id in self.processed_categories:
                                continue
                                
                            level = int(node.get_attribute('aria-level'))
                            category_name = self.get_category_name(node)
                            
                            if not category_name:
                                continue
                            
                            full_path = self.build_category_path(node, level)
                            main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
                            parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
                            final_category_name = category_name

                            has_children = self.has_expand_button(node)
                            children_count = self.count_children(level) if has_children else 0

                            pricing_data = self.get_pricing_info(node)

                            if pricing_data:
                                for price_info in pricing_data:
                                    record = {
                                        'pelna_sciezka_kategorii': full_path,
                                        'kategoria_glowna': main_category,
                                        'parent_kategorii': parent_category,
                                        'nazwa_kategorii_ostatecznej': final_category_name,
                                        'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                        'liczba_podkategorii': children_count,
                                        'okres_czasowy': price_info['period'],
                                        'cena_minimalna_pln': price_info['min_price'],
                                        'cena_maksymalna_pln': price_info['max_price']
                                    }
                                    writer.writerow(record)
                                    record_count += 1
                            else:
                                record = {
                                    'pelna_sciezka_kategorii': full_path,
                                    'kategoria_glowna': main_category,
                                    'parent_kategorii': parent_category,
                                    'nazwa_kategorii_ostatecznej': final_category_name,
                                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                    'liczba_podkategorii': children_count,
                                    'okres_czasowy': 'Brak danych cenowych',
                                    'cena_minimalna_pln': 'N/A',
                                    'cena_maksymalna_pln': 'N/A'
                                }
                                writer.writerow(record)
                                record_count += 1
                            
                            self.processed_categories.add(node_id)
                            
                            if record_count % 50 == 0:
                                logger.info(f"Zapisano {record_count} rekordów...")
                                file.flush()
                                
                        except Exception as e:
                            logger.warning(f"Błąd przetwarzania węzła: {e}")
                    
                    break
                
                # Przetwórz i zapisz obecne węzły PRZED rozwijaniem
                current_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
                for node in current_nodes:
                    try:
                        node_id = node.get_attribute('data-cy')
                        if node_id in self.processed_categories:
                            continue

                        level = int(node.get_attribute('aria-level'))
                        category_name = self.get_category_name(node)

                        if not category_name:
                            continue

                        full_path = self.build_category_path(node, level)
                        main_category = full_path.split(' > ')[0] if ' > ' in full_path else full_path
                        parent_category = ' > '.join(full_path.split(' > ')[:-1]) if ' > ' in full_path else ""
                        final_category_name = category_name  # Nazwa ostatecznej kategorii

                        has_children = self.has_expand_button(node)
                        children_count = 0  # Uproszczenie

                        pricing_data = self.get_pricing_info(node)

                        # Jeśli brak danych cenowych, poczekaj i spróbuj ponownie
                        if not pricing_data:
                            time.sleep(2)
                            pricing_data = self.get_pricing_info(node)

                        if pricing_data:
                            for price_info in pricing_data:
                                record = {
                                    'pelna_sciezka_kategorii': full_path,
                                    'kategoria_glowna': main_category,
                                    'parent_kategorii': parent_category,
                                    'nazwa_kategorii_ostatecznej': final_category_name,
                                    'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                    'liczba_podkategorii': children_count,
                                    'okres_czasowy': price_info['period'],
                                    'cena_minimalna_pln': price_info['min_price'],
                                    'cena_maksymalna_pln': price_info['max_price']
                                }
                                writer.writerow(record)
                                record_count += 1
                        else:
                            record = {
                                'pelna_sciezka_kategorii': full_path,
                                'kategoria_glowna': main_category,
                                'parent_kategorii': parent_category,
                                'nazwa_kategorii_ostatecznej': final_category_name,
                                'czy_kategoria_ostateczna': 'Nie' if has_children else 'Tak',
                                'liczba_podkategorii': children_count,
                                'okres_czasowy': 'Brak danych cenowych',
                                'cena_minimalna_pln': 'N/A',
                                'cena_maksymalna_pln': 'N/A'
                            }
                            writer.writerow(record)
                            record_count += 1

                        self.processed_categories.add(node_id)

                        if record_count % 10 == 0:
                            logger.info(f"Zapisano {record_count} rekordów...")
                            file.flush()

                    except Exception as e:
                        logger.warning(f"Błąd przetwarzania węzła: {e}")

                # Rozwija WSZYSTKIE węzły w tej iteracji (nie tylko 10)
                expanded_count = 0
                for node in nodes_to_expand:
                    try:
                        expand_button = self.get_expand_button(node)
                        if expand_button:
                            self.driver.execute_script("arguments[0].click();", expand_button)
                            expanded_count += 1
                            time.sleep(0.3)  # Krótka pauza między kliknięciami

                            # Co 20 rozwinięć zrób dłuższą pauzę
                            if expanded_count % 20 == 0:
                                logger.info(f"Rozwinięto {expanded_count} węzłów...")
                                time.sleep(2)

                    except Exception as e:
                        logger.warning(f"Błąd rozwijania węzła: {e}")

                logger.info(f"Rozwinięto {expanded_count} węzłów")
                time.sleep(3)  # Czas na załadowanie wszystkich nowych węzłów
            
            logger.info(f"Zapisano łącznie {record_count} rekordów do {self.csv_file}")

    def cleanup(self):
        """Zamknięcie przeglądarki."""
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka zamknięta")

    def run(self):
        """Uruchamia scraper."""
        try:
            logger.info("=== ROZPOCZĘCIE SCRAPINGU ===")
            
            self.setup_driver()
            self.navigate_to_page()
            self.process_and_save_categories()
            
            logger.info("=== SCRAPING ZAKOŃCZONY POMYŚLNIE ===")
            
        except Exception as e:
            logger.error(f"Błąd podczas scrapingu: {e}")
        finally:
            self.cleanup()

def main():
    """Funkcja główna."""
    scraper = SimpleCeneoScraper()
    scraper.run()

if __name__ == "__main__":
    main()
