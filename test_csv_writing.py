#!/usr/bin/env python3
"""
Test zapisywania danych do CSV - rozwija tylko kilka kategorii i pokazuje jak zapisuje dane.
"""

import logging
import csv
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from typing import Dict, List

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestCSVWriter:
    def __init__(self):
        self.driver = None
        
    def setup_driver(self):
        """Konfiguracja przeglądarki Chrome."""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Przeglądarka uruchomiona")

    def navigate_to_page(self):
        """Nawigacja do strony Ceneo."""
        self.driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
        logger.info("Nawigacja do strony zakończona")
        
        # Czeka na załadowanie
        try:
            WebDriverWait(self.driver, 30).until_not(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
            )
            logger.info("Splash screen zniknął")
        except:
            logger.info("Splash screen nie został wykryty lub już zniknął")
        
        time.sleep(10)
        logger.info("Drzewo kategorii zostało załadowane")

    def expand_few_categories(self, max_categories=3):
        """Rozwija kilka pierwszych kategorii dla testu."""
        logger.info(f"Rozwijanie pierwszych {max_categories} kategorii...")
        
        # Znajdź przyciski rozwijania
        expand_buttons = self.driver.find_elements(By.XPATH, "//mat-icon[text()='chevron_right']")[:max_categories]
        logger.info(f"Znaleziono {len(expand_buttons)} przycisków do rozwinięcia")
        
        for i, button in enumerate(expand_buttons):
            try:
                self.driver.execute_script("arguments[0].click();", button)
                logger.info(f"Kliknięto przycisk {i+1}")
                time.sleep(3)  # Czeka na załadowanie podkategorii
            except Exception as e:
                logger.warning(f"Błąd podczas klikania przycisku {i+1}: {e}")

    def extract_category_name_from_node(self, node):
        """Wyodrębnia nazwę kategorii z węzła."""
        try:
            node_text = node.text.strip()
            if not node_text:
                return None
            
            lines = [line.strip() for line in node_text.split('\n') if line.strip()]
            
            # Usuń ikony Material Design
            filtered_lines = []
            for line in lines:
                if line not in ['chevron_right', 'expand_more'] and not line.endswith('PLN'):
                    filtered_lines.append(line)
            
            if filtered_lines:
                return filtered_lines[0]  # Pierwsza linia to nazwa kategorii
            
            return None
        except Exception as e:
            logger.debug(f"Błąd podczas wyodrębniania nazwy kategorii: {e}")
            return None

    def extract_pricing_data_from_node(self, node):
        """Wyodrębnia dane cenowe z węzła."""
        try:
            node_text = node.text.strip()
            if not node_text:
                return []
            
            lines = [line.strip() for line in node_text.split('\n') if line.strip()]
            price_lines = [line for line in lines if "PLN" in line]
            
            pricing_data = []
            for price_line in price_lines:
                # Parsuj linię z cenami, np. "styczeń - marzec: 0,52 - 1,22 PLN"
                if ":" in price_line and "PLN" in price_line:
                    parts = price_line.split(":")
                    if len(parts) == 2:
                        period = parts[0].strip()
                        price_part = parts[1].strip().replace("PLN", "").strip()
                        
                        if " - " in price_part:
                            price_range = price_part.split(" - ")
                            if len(price_range) == 2:
                                min_price = price_range[0].strip()
                                max_price = price_range[1].strip()
                                pricing_data.append({
                                    'period': period,
                                    'min_price': min_price,
                                    'max_price': max_price
                                })
            
            return pricing_data
        except Exception as e:
            logger.debug(f"Błąd podczas wyodrębniania danych cenowych: {e}")
            return []

    def node_has_children(self, node):
        """Sprawdza czy węzeł ma dzieci."""
        try:
            # Sprawdź czy ma ikonę chevron_right lub expand_more
            icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
            for icon in icons:
                if icon.text in ['chevron_right', 'expand_more']:
                    return True
            return False
        except:
            return False

    def test_data_extraction(self):
        """Testuje wyodrębnianie danych i zapis do CSV."""
        logger.info("Rozpoczęcie testowania wyodrębniania danych...")
        
        # Znajdź wszystkie węzły drzewa
        tree_nodes = self.driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
        logger.info(f"Znaleziono {len(tree_nodes)} węzłów drzewa")
        
        # Pogrupuj węzły według poziomów
        nodes_by_level = {}
        for node in tree_nodes:
            try:
                level = int(node.get_attribute("aria-level"))
                if level not in nodes_by_level:
                    nodes_by_level[level] = []
                nodes_by_level[level].append(node)
            except:
                continue
        
        logger.info(f"Węzły pogrupowane według poziomów: {dict((k, len(v)) for k, v in nodes_by_level.items())}")
        
        # Otwórz plik CSV do zapisu
        csv_file = 'test_ceneo_output.csv'
        record_count = 0
        category_paths = {}
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as file:
            fieldnames = [
                'pelna_sciezka_kategorii', 'kategoria_glowna', 'parent_kategorii',
                'czy_kategoria_ostateczna', 'liczba_podkategorii', 'okres_czasowy',
                'cena_minimalna_pln', 'cena_maksymalna_pln'
            ]
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            
            # Przetwarzaj poziom po poziomie
            for level in sorted(nodes_by_level.keys()):
                nodes = nodes_by_level[level]
                logger.info(f"Przetwarzanie poziomu {level} ({len(nodes)} węzłów)")
                
                for i, node in enumerate(nodes):
                    try:
                        node_id = node.get_attribute('data-cy')
                        if not node_id:
                            continue
                        
                        # Wyodrębnij nazwę kategorii
                        category_name = self.extract_category_name_from_node(node)
                        if not category_name or category_name in ['chevron_right', 'expand_more']:
                            continue
                        
                        logger.info(f"  Przetwarzanie kategorii: '{category_name}' (poziom {level})")
                        
                        # Buduj ścieżkę kategorii (uproszczona wersja)
                        if level == 1:
                            full_path = category_name
                            main_category = category_name
                            parent_category = ""
                        else:
                            # Dla wyższych poziomów - uproszczenie
                            full_path = f"Poziom{level} > {category_name}"
                            main_category = category_name
                            parent_category = f"Poziom{level-1}"
                        
                        # Zapisz ścieżkę dla tego węzła
                        category_paths[node_id] = full_path
                        
                        # Sprawdź czy ma dzieci
                        has_children = self.node_has_children(node)
                        children_count = 0  # Uproszczenie
                        
                        # Wyodrębnij dane cenowe
                        pricing_data = self.extract_pricing_data_from_node(node)
                        
                        if pricing_data:
                            for period_data in pricing_data:
                                record = {
                                    'pelna_sciezka_kategorii': full_path,
                                    'kategoria_glowna': main_category,
                                    'parent_kategorii': parent_category,
                                    'czy_kategoria_ostateczna': 'Tak' if not has_children else 'Nie',
                                    'liczba_podkategorii': children_count,
                                    'okres_czasowy': period_data.get('period', ''),
                                    'cena_minimalna_pln': period_data.get('min_price', ''),
                                    'cena_maksymalna_pln': period_data.get('max_price', '')
                                }
                                writer.writerow(record)
                                record_count += 1
                                logger.info(f"    Zapisano rekord: {period_data.get('period', '')} - {period_data.get('min_price', '')} - {period_data.get('max_price', '')} PLN")
                        else:
                            # Jeśli brak danych cenowych
                            record = {
                                'pelna_sciezka_kategorii': full_path,
                                'kategoria_glowna': main_category,
                                'parent_kategorii': parent_category,
                                'czy_kategoria_ostateczna': 'Tak' if not has_children else 'Nie',
                                'liczba_podkategorii': children_count,
                                'okres_czasowy': '',
                                'cena_minimalna_pln': '',
                                'cena_maksymalna_pln': ''
                            }
                            writer.writerow(record)
                            record_count += 1
                            logger.info(f"    Zapisano rekord bez danych cenowych")
                        
                    except Exception as e:
                        logger.warning(f"Błąd podczas przetwarzania węzła: {e}")
                        continue
        
        logger.info(f"Zapisano {record_count} rekordów do pliku {csv_file}")
        return record_count

    def cleanup(self):
        """Zamknięcie przeglądarki."""
        if self.driver:
            self.driver.quit()
            logger.info("Przeglądarka zamknięta")

    def run_test(self):
        """Uruchamia test."""
        try:
            logger.info("=== ROZPOCZĘCIE TESTU ZAPISYWANIA CSV ===")
            
            self.setup_driver()
            self.navigate_to_page()
            
            # Pokaż stan przed rozwinięciem
            logger.info("\n--- STAN PRZED ROZWINIĘCIEM ---")
            initial_count = self.test_data_extraction()
            
            # Rozwija kilka kategorii
            self.expand_few_categories(3)
            
            # Pokaż stan po rozwinięciu
            logger.info("\n--- STAN PO ROZWINIĘCIU ---")
            final_count = self.test_data_extraction()
            
            logger.info(f"\n=== PODSUMOWANIE ===")
            logger.info(f"Rekordów przed rozwinięciem: {initial_count}")
            logger.info(f"Rekordów po rozwinięciu: {final_count}")
            logger.info(f"Przyrost: {final_count - initial_count}")
            
        except Exception as e:
            logger.error(f"Błąd podczas testu: {e}")
        finally:
            self.cleanup()

def main():
    """Funkcja główna."""
    test = TestCSVWriter()
    test.run_test()

if __name__ == "__main__":
    main()
