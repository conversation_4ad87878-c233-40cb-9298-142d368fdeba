#!/usr/bin/env python3
"""
Test rozwijania węzłów w zakładce prowizji "Kup Teraz".
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def navigate_to_kup_teraz(driver):
    driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
    logger.info("Nawigacja do strony zakończona")
    
    try:
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty")
    
    time.sleep(5)
    
    # Przełącz na zakładkę "Kup Teraz"
    kup_teraz_tab = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Kup Teraz')]"))
    )
    driver.execute_script("arguments[0].click();", kup_teraz_tab)
    logger.info("Przełączono na zakładkę 'Kup Teraz'")
    time.sleep(5)

def has_expand_button(node):
    """Sprawdza czy węzeł ma przycisk rozwijania."""
    try:
        icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
        for icon in icons:
            if icon.text == 'chevron_right':
                return True
        return False
    except:
        return False

def get_expand_button(node):
    """Zwraca przycisk rozwijania dla węzła."""
    try:
        icons = node.find_elements(By.CSS_SELECTOR, "mat-icon")
        for icon in icons:
            if icon.text == 'chevron_right':
                return icon
        return None
    except:
        return None

def get_category_name(node):
    """Wyciąga nazwę kategorii z węzła."""
    try:
        text = node.text.strip()
        if not text:
            return None
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        for line in lines:
            if line not in ['chevron_right', 'expand_more'] and '%' not in line and 'zł' not in line:
                return line
        
        return None
    except:
        return None

def test_expansion_levels(driver):
    """Testuje rozwijanie na różnych poziomach."""
    
    logger.info("\n=== TEST ROZWIJANIA WĘZŁÓW ===")
    
    iteration = 0
    
    while iteration < 5:  # Maksymalnie 5 iteracji
        iteration += 1
        logger.info(f"\n--- ITERACJA {iteration} ---")
        
        # Znajdź wszystkie węzły
        all_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
        logger.info(f"Znaleziono {len(all_nodes)} węzłów")
        
        # Sprawdź poziomy węzłów
        levels_count = {}
        for node in all_nodes:
            try:
                level = int(node.get_attribute('aria-level'))
                levels_count[level] = levels_count.get(level, 0) + 1
            except:
                pass
        
        logger.info(f"Węzły na poziomach: {levels_count}")
        
        # Znajdź węzły do rozwinięcia
        nodes_to_expand = []
        for i, node in enumerate(all_nodes):
            if has_expand_button(node):
                level = node.get_attribute('aria-level')
                category_name = get_category_name(node)
                nodes_to_expand.append((i, node, level, category_name))
        
        logger.info(f"Węzłów do rozwinięcia: {len(nodes_to_expand)}")
        
        if not nodes_to_expand:
            logger.info("Brak węzłów do rozwinięcia - koniec")
            break
        
        # Pokaż pierwsze 5 węzłów do rozwinięcia
        for i, (idx, node, level, name) in enumerate(nodes_to_expand[:5]):
            logger.info(f"  {i}: Poziom {level}, Pozycja {idx}, Nazwa: {name}")
        
        # Rozwiń pierwsze 10 węzłów
        expanded_count = 0
        for idx, node, level, name in nodes_to_expand[:10]:
            try:
                expand_button = get_expand_button(node)
                if expand_button:
                    logger.info(f"Rozwijam: {name} (poziom {level})")
                    driver.execute_script("arguments[0].click();", expand_button)
                    expanded_count += 1
                    time.sleep(0.5)
            except Exception as e:
                logger.warning(f"Błąd rozwijania {name}: {e}")
        
        logger.info(f"Rozwinięto {expanded_count} węzłów")
        time.sleep(3)  # Czekaj na załadowanie nowych węzłów
    
    # Finalne podsumowanie
    final_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
    final_levels = {}
    for node in final_nodes:
        try:
            level = int(node.get_attribute('aria-level'))
            final_levels[level] = final_levels.get(level, 0) + 1
        except:
            pass
    
    logger.info(f"\n=== FINALNE PODSUMOWANIE ===")
    logger.info(f"Łączna liczba węzłów: {len(final_nodes)}")
    logger.info(f"Węzły na poziomach: {final_levels}")
    
    # Sprawdź przykłady z różnych poziomów
    logger.info(f"\n=== PRZYKŁADY Z RÓŻNYCH POZIOMÓW ===")
    for level in sorted(final_levels.keys()):
        level_nodes = [n for n in final_nodes if n.get_attribute('aria-level') == str(level)]
        if level_nodes:
            example_node = level_nodes[0]
            example_name = get_category_name(example_node)
            example_text = example_node.text.strip()[:100]
            logger.info(f"Poziom {level}: {example_name} - '{example_text}...'")

def main():
    driver = setup_driver()
    
    try:
        navigate_to_kup_teraz(driver)
        test_expansion_levels(driver)
        
    except Exception as e:
        logger.error(f"Błąd: {e}")
    finally:
        input("Naciśnij Enter aby zamknąć przeglądarkę...")
        driver.quit()

if __name__ == "__main__":
    main()
