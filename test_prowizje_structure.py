#!/usr/bin/env python3
"""
Test do sprawdzenia struktury danych prowizyjnych na zakładce "Kup Teraz".
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def navigate_to_kup_teraz(driver):
    driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
    logger.info("Nawigacja do strony zakończona")
    
    try:
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty")
    
    time.sleep(5)
    
    # Znajdź i kliknij zakładkę "Kup Teraz"
    logger.info("Szukam zakładki 'Kup Teraz'...")
    
    # Sprawdź wszystkie możliwe selektory dla zakładki
    possible_selectors = [
        "//span[contains(text(), 'Kup Teraz')]",
        "//a[contains(text(), 'Kup Teraz')]",
        "//button[contains(text(), 'Kup Teraz')]",
        "//div[contains(text(), 'Kup Teraz')]",
        "//mat-tab[contains(text(), 'Kup Teraz')]",
        "//a[contains(@href, 'BuyNow')]",
        "//a[contains(@href, 'buynow')]",
        "[data-cy*='buynow']",
        "[data-cy*='BuyNow']",
        ".mat-tab-label[aria-selected='false']"
    ]
    
    tab_found = False
    for selector in possible_selectors:
        try:
            if selector.startswith("//"):
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            if elements:
                logger.info(f"Znaleziono {len(elements)} elementów dla selektora: {selector}")
                for i, elem in enumerate(elements):
                    try:
                        text = elem.text.strip()
                        tag = elem.tag_name
                        classes = elem.get_attribute('class')
                        href = elem.get_attribute('href')
                        logger.info(f"  Element {i}: <{tag}> text='{text}' class='{classes}' href='{href}'")
                        
                        if 'kup' in text.lower() or 'teraz' in text.lower() or 'buynow' in str(href).lower():
                            logger.info(f"Próbuję kliknąć element {i}...")
                            driver.execute_script("arguments[0].click();", elem)
                            time.sleep(5)
                            tab_found = True
                            logger.info("Kliknięto zakładkę!")
                            break
                    except Exception as e:
                        logger.warning(f"Błąd z elementem {i}: {e}")
                
                if tab_found:
                    break
        except Exception as e:
            logger.warning(f"Błąd z selektorem {selector}: {e}")
    
    if not tab_found:
        logger.error("Nie znaleziono zakładki 'Kup Teraz'!")
        # Sprawdź wszystkie zakładki
        all_tabs = driver.find_elements(By.CSS_SELECTOR, ".mat-tab-label, .tab, [role='tab']")
        logger.info(f"Wszystkie zakładki ({len(all_tabs)}):")
        for i, tab in enumerate(all_tabs):
            try:
                text = tab.text.strip()
                classes = tab.get_attribute('class')
                logger.info(f"  Zakładka {i}: text='{text}' class='{classes}'")
            except:
                pass
    
    logger.info("Strona załadowana")

def analyze_prowizje_structure(driver):
    """Analizuje strukturę danych prowizyjnych."""
    
    logger.info("\n=== ANALIZA STRUKTURY PROWIZJI ===")
    
    # Sprawdź wszystkie węzły drzewa
    all_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
    logger.info(f"Znaleziono {len(all_nodes)} węzłów drzewa")
    
    # Analizuj pierwsze 5 węzłów
    for i, node in enumerate(all_nodes[:5]):
        logger.info(f"\n--- WĘZEŁ {i+1} ---")
        
        try:
            level = node.get_attribute('aria-level')
            data_cy = node.get_attribute('data-cy')
            full_text = node.text.strip()
            
            logger.info(f"Poziom: {level}, data-cy: {data_cy}")
            logger.info(f"Pełny tekst:\n{repr(full_text)}")
            
            # Sprawdź linie tekstu
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            logger.info(f"Linie ({len(lines)}):")
            for j, line in enumerate(lines):
                has_percent = '%' in line
                has_colon = ':' in line
                has_dash = '-' in line
                logger.info(f"  {j}: {repr(line)} [%: {has_percent}, ':': {has_colon}, '-': {has_dash}]")
            
            # Sprawdź wszystkie elementy potomne
            all_children = node.find_elements(By.CSS_SELECTOR, "*")
            logger.info(f"Elementy potomne: {len(all_children)}")
            
            # Szukaj elementów z prowizjami
            prowizja_elements = []
            for child in all_children:
                try:
                    child_text = child.text.strip()
                    child_tag = child.tag_name
                    child_classes = child.get_attribute('class')
                    
                    if '%' in child_text or 'prowizja' in child_classes.lower():
                        prowizja_elements.append({
                            'tag': child_tag,
                            'text': child_text,
                            'classes': child_classes
                        })
                except:
                    pass
            
            if prowizja_elements:
                logger.info("ELEMENTY Z PROWIZJAMI:")
                for elem in prowizja_elements:
                    logger.info(f"  <{elem['tag']}> class='{elem['classes']}' text='{elem['text']}'")
            else:
                logger.info("BRAK ELEMENTÓW Z PROWIZJAMI")
                
        except Exception as e:
            logger.error(f"Błąd analizy węzła {i+1}: {e}")
    
    # Sprawdź czy są jakieś elementy z % na całej stronie
    logger.info(f"\n=== WSZYSTKIE ELEMENTY Z % NA STRONIE ===")
    all_percent_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '%')]")
    logger.info(f"Znaleziono {len(all_percent_elements)} elementów z %")
    
    for i, elem in enumerate(all_percent_elements[:10]):  # Tylko pierwsze 10
        try:
            text = elem.text.strip()
            tag = elem.tag_name
            classes = elem.get_attribute('class')
            logger.info(f"  {i}: <{tag}> class='{classes}' text='{text[:100]}...'")
        except:
            pass
    
    # Sprawdź tabele
    logger.info(f"\n=== TABELE NA STRONIE ===")
    tables = driver.find_elements(By.CSS_SELECTOR, "table, .table, mat-table")
    logger.info(f"Znaleziono {len(tables)} tabel")
    
    for i, table in enumerate(tables):
        try:
            table_text = table.text.strip()[:200]
            table_classes = table.get_attribute('class')
            logger.info(f"  Tabela {i}: class='{table_classes}' text='{table_text}...'")
            
            # Sprawdź nagłówki tabeli
            headers = table.find_elements(By.CSS_SELECTOR, "th, .mat-header-cell")
            if headers:
                header_texts = [h.text.strip() for h in headers]
                logger.info(f"    Nagłówki: {header_texts}")
        except:
            pass

def main():
    driver = setup_driver()
    
    try:
        navigate_to_kup_teraz(driver)
        analyze_prowizje_structure(driver)
        
    except Exception as e:
        logger.error(f"Błąd: {e}")
    finally:
        input("Naciśnij Enter aby zamknąć przeglądarkę...")
        driver.quit()

if __name__ == "__main__":
    main()
