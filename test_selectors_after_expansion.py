#!/usr/bin/env python3
"""
Test różnych selektorów CSS po rozwinięciu kategorii.
"""

import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    """Konfiguracja przeglądarki Chrome."""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")

    # Użyj chromedriver z brew
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def wait_for_page_load(driver):
    """Czeka na załadowanie strony."""
    try:
        # Czeka na zniknięcie splash screen
        logger.info("Oczekiwanie na zniknięcie splash screen...")
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty lub już zniknął")
    
    # Dodatkowe oczekiwanie na załadowanie drzewa
    time.sleep(10)
    logger.info("Drzewo kategorii zostało załadowane")

def expand_first_few_categories(driver):
    """Rozwija pierwsze kilka kategorii dla testu."""
    logger.info("Rozwijanie pierwszych kilku kategorii...")

    # Znajdź pierwsze 3 przyciski rozwijania
    expand_buttons = driver.find_elements(By.XPATH, "//mat-icon[text()='chevron_right']")[:3]

    logger.info(f"Znaleziono {len(expand_buttons)} przycisków do rozwinięcia")

    for i, button in enumerate(expand_buttons):
        try:
            driver.execute_script("arguments[0].click();", button)
            logger.info(f"Kliknięto przycisk {i+1}")
            time.sleep(2)  # Czeka na załadowanie podkategorii
        except Exception as e:
            logger.warning(f"Błąd podczas klikania przycisku {i+1}: {e}")

def test_selectors(driver):
    """Testuje różne selektory CSS."""
    selectors_to_test = [
        "[data-node-id]",
        "[data-cy]",
        "mat-tree-node",
        ".mat-tree-node",
        "[aria-level]",
        "mat-tree-node[aria-level]",
        ".cdk-tree-node",
        "[role=\"treeitem\"]",
        "div[role=\"treeitem\"]",
        ".mat-tree-node-content",
        "button[aria-expanded]",
        ".mat-tree-node-toggle",
        "mat-icon + span",
        ".category-name",
        ".price-info"
    ]
    
    logger.info("Testowanie selektorów CSS...")
    
    for selector in selectors_to_test:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            logger.info(f"Selektor '{selector}': znaleziono {len(elements)} elementów")
            
            if elements and len(elements) > 0:
                # Pokaż przykład tekstu z pierwszego elementu
                try:
                    sample_text = elements[0].text.strip()[:100]
                    logger.info(f"  Przykład tekstu: '{sample_text}'")
                    
                    # Pokaż atrybuty pierwszego elementu
                    attrs = driver.execute_script("""
                        var attrs = {};
                        for (var i = 0; i < arguments[0].attributes.length; i++) {
                            var attr = arguments[0].attributes[i];
                            attrs[attr.name] = attr.value;
                        }
                        return attrs;
                    """, elements[0])
                    logger.info(f"  Atrybuty: {attrs}")
                except Exception as e:
                    logger.debug(f"  Błąd podczas pobierania tekstu/atrybutów: {e}")
        except Exception as e:
            logger.debug(f"Selektor '{selector}': błąd - {e}")

def main():
    """Funkcja główna."""
    driver = None
    try:
        logger.info("Rozpoczęcie testu selektorów...")
        
        driver = setup_driver()
        logger.info("Przeglądarka uruchomiona")
        
        # Nawigacja do strony
        driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
        logger.info("Nawigacja do strony zakończona")
        
        # Czeka na załadowanie
        wait_for_page_load(driver)
        
        # Test selektorów przed rozwinięciem
        logger.info("\n=== SELEKTORY PRZED ROZWINIĘCIEM ===")
        test_selectors(driver)
        
        # Rozwija kilka kategorii
        expand_first_few_categories(driver)
        
        # Test selektorów po rozwinięciu
        logger.info("\n=== SELEKTORY PO ROZWINIĘCIU ===")
        test_selectors(driver)
        
        logger.info("Test zakończony pomyślnie!")
        
    except Exception as e:
        logger.error(f"Błąd podczas testu: {e}")
    finally:
        if driver:
            driver.quit()
            logger.info("Przeglądarka zamknięta")

if __name__ == "__main__":
    main()
