#!/usr/bin/env python3
"""
Test sprawdzający dane cenowe w podkategoriach po rozwinięciu.
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service('/opt/homebrew/bin/chromedriver')
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def navigate_to_page(driver):
    driver.get("https://shops.ceneo.pl/Pricelist/Cpc")
    logger.info("Nawigacja do strony zakończona")
    
    try:
        WebDriverWait(driver, 30).until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".splash-screen, .loading"))
        )
        logger.info("Splash screen zniknął")
    except:
        logger.info("Splash screen nie został wykryty")
    
    time.sleep(5)
    logger.info("Strona załadowana")

def analyze_subcategory_pricing(driver):
    """Analizuje dane cenowe w podkategoriach."""
    
    # Znajdź pierwszą kategorię główną
    all_nodes = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
    logger.info(f"Znaleziono {len(all_nodes)} węzłów na początku")
    
    first_node = all_nodes[0]
    logger.info(f"Pierwsza kategoria: {first_node.text.split()[1] if len(first_node.text.split()) > 1 else 'Nieznana'}")
    
    # Rozwiń pierwszą kategorię
    expand_button = first_node.find_element(By.CSS_SELECTOR, "mat-icon")
    if expand_button.text == 'chevron_right':
        logger.info("Rozwijam pierwszą kategorię...")
        driver.execute_script("arguments[0].click();", expand_button)
        time.sleep(3)  # Czekaj na załadowanie
        
        # Sprawdź węzły po rozwinięciu
        all_nodes_after = driver.find_elements(By.CSS_SELECTOR, "mat-tree-node")
        logger.info(f"Po rozwinięciu: {len(all_nodes_after)} węzłów")
        
        # Znajdź podkategorie (poziom 2)
        subcategories = [node for node in all_nodes_after if node.get_attribute('aria-level') == '2']
        logger.info(f"Znaleziono {len(subcategories)} podkategorii")
        
        # Analizuj pierwsze 3 podkategorie
        for i, subcat in enumerate(subcategories[:3]):
            logger.info(f"\n=== PODKATEGORIA {i+1} ===")
            
            # Podstawowe info
            data_cy = subcat.get_attribute('data-cy')
            full_text = subcat.text.strip()
            logger.info(f"data-cy: {data_cy}")
            logger.info(f"Pełny tekst:\n{repr(full_text)}")
            
            # Sprawdź linie
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            logger.info(f"Linie ({len(lines)}):")
            for j, line in enumerate(lines):
                logger.info(f"  {j}: {repr(line)}")
            
            # Sprawdź czy ma dane cenowe
            has_pricing = any('PLN' in line and ':' in line for line in lines)
            logger.info(f"Ma dane cenowe: {has_pricing}")
            
            if not has_pricing:
                logger.info("BRAK DANYCH CENOWYCH - sprawdzam czy trzeba poczekać...")
                time.sleep(2)  # Dodatkowe oczekiwanie
                
                # Sprawdź ponownie
                updated_text = subcat.text.strip()
                updated_lines = [line.strip() for line in updated_text.split('\n') if line.strip()]
                has_pricing_after = any('PLN' in line and ':' in line for line in updated_lines)
                
                logger.info(f"Po dodatkowym oczekiwaniu - ma dane cenowe: {has_pricing_after}")
                if has_pricing_after:
                    logger.info(f"Zaktualizowany tekst:\n{repr(updated_text)}")

def main():
    driver = setup_driver()
    
    try:
        navigate_to_page(driver)
        analyze_subcategory_pricing(driver)
        
    except Exception as e:
        logger.error(f"Błąd: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
